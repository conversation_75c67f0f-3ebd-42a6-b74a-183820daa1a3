<template>
  <el-dropdown trigger="click" @command="handleLanguageChange">
    <div>
      <svg-icon icon-class="language" :size="size" />
    </div>
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item
          v-for="item in langOptions"
          :key="item.value"
          :disabled="appStore.language === item.value"
          :command="item.value"
        >
          {{ item.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup>
import { useI18n } from "vue-i18n";
import useAppStore from "@/store/modules/app";

const { proxy } = getCurrentInstance()

defineProps({
  size: {
    type: String,
    required: false,
  },
});

const langOptions = [
  { label: "中文", value: 'zh-CN' },
  // { label: "English", value: 'en' },
  { label: "Русский язык", value: 'ru' },       // 俄语（ru）
  // { label: "Bahasa Indonesia", value: 'id' },   // 印度尼西亚语（id）
  // { label: "한국어", value: 'ko' },             // 韩语
  // { label: "Tiếng Việt", value: 'vi' },         // 越南语
];

const appStore = useAppStore();
const { locale, t } = useI18n();

function handleLanguageChange(lang) {
  locale.value = lang;
  appStore.changeLanguage(lang);
  
  proxy.$modal.msgSuccess(t("langSelect.message.success"));
}
</script>
