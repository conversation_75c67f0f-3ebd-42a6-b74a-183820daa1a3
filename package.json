{"name": "odc-oms-web", "version": "1.0.0", "description": "国际直采运营管理系统", "author": "国际直采运营", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@microsoft/fetch-event-source": "^2.0.1", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "^10.11.0", "ali-oss": "^6.21.0", "axios": "0.28.1", "big.js": "^6.2.2", "crypto-js": "^4.2.0", "decimal.js": "^10.4.3", "echarts": "^5.5.1", "element-plus": "2.7.6", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-base64": "^3.7.7", "js-cookie": "3.0.5", "jsbarcode": "^3.11.6", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "mitt": "^3.0.1", "nprogress": "0.2.0", "pinia": "2.1.7", "swiper": "^11.1.14", "vue": "3.4.31", "vue-cropper": "1.1.1", "vue-draggable-plus": "^0.6.0", "vue-echarts": "^7.0.3", "vue-i18n": "^9.14.0", "vue-router": "4.4.0"}, "devDependencies": {"@iconify-json/ep": "^1.1.15", "@rollup/plugin-inject": "^5.0.2", "@vitejs/plugin-vue": "5.0.5", "mockjs": "^1.1.0", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-icons": "^0.19.3", "unplugin-vue-components": "^0.27.4", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-mock": "^2.9.8", "vite-plugin-svg-icons": "2.0.1"}}