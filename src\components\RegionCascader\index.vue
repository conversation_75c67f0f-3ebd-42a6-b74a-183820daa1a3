<template>
  <div class="region-cascader">
    <el-cascader ref="cas" style="width: 100%;" :options="options" v-model="casValue" @change="handleChange"
      :props="casProps" :placeholder="placeholder"></el-cascader>
  </div>
</template>

<script>
import useAppStore from "@/store/modules/app";
const appStore = useAppStore();
const language = computed(() => appStore.language ? appStore.language : 'zh-cn');
import { getCountryList, getCityListByCountryId } from "@/api/common";
export default {
  name: 'regionCascader',
  props: {
    value: {
      type: Array,
      default: [],
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
  },
  data() {
    return {
      options: [],
      casProps:
      {
        value: 'id',
        label: language.value === 'zh-cn' ? 'cnName' : 'enName',
        children: 'child',
        lazy: true,
        lazyLoad: this.handleNode,
        expandTrigger: 'click',
      }
      ,
      selectedOptions: []
    }
  },
  computed: {
    casValue: {
      set(val) {
        this.$emit('update:value', val)
      },
      get(val) {
        return this.value
      }
    }
  },
  mounted() {

  },
  methods: {
    handleChange(val) {
      const { pathNodes, pathLabels, pathValues } = this.$refs.cas.getCheckedNodes()[0]
      this.$emit('provincesValueChang', pathNodes, pathLabels, pathValues)
    },

    handleNode(node, resolve) {
      if (!node.level) {
        getCountryList({}).then((res) => {
          const { code, data, message } = res
          if (code === 0) {
            data.map(item => item.level = 1)
            resolve(data);
          } else {
            this.$message.error(message)
          }
        })

      } else if (node.level === 1) {
        console.log('node', node.value);
        getCityListByCountryId(node.value).then((res) => {
          const { code, data, message } = res
          if (code === 0) {
            data.map((el) => { el.leaf = true; return el })
            resolve(data);
          } else {
            this.$message.error(message)
          }
        })
      } else {
        resolve();
      }

    }
  }
}
</script>
