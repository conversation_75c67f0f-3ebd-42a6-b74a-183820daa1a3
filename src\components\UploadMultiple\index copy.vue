<template>
  <div class="component-upload-image">
    <el-upload
        multiple
        :disabled="disabledVal"
        :list-type="listType"
        :on-success="handleUploadSuccess"
        :before-upload="handleBeforeUpload"
        :http-request="uploadFile"
        :limit="limit"
        :on-error="handleUploadError"
        :on-exceed="handleExceed"
        ref="imageUpload"
        :before-remove="handleDelete"
        :file-list="fileList"
        :on-preview="handlePictureCardPreview"
        :class="{ hide: fileList.length >= limit }"
    >
      <!-- 自定义图片展示-->
      <template #file="{ file, index }" v-if="customFileShow">
        <slot :file="file" :index="index">
          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
          <label v-if="file.status === 'success'" class="el-upload-list__item-status-label">
            <el-icon class="el-icon-upload-success el-icon-check" color="#fff"><Check /></el-icon>
          </label>
          <span class="el-upload-list__item-actions">
            <span
              class="el-upload-list__item-preview"
              @click="handlePictureCardPreview(file)"
            >
              <el-icon><zoom-in /></el-icon>
            </span>
            <span
              class="el-upload-list__item-delete"
              @click="handleDelete(file)"
            >
              <el-icon><Delete /></el-icon>
            </span>
          </span>
        </slot>
      </template>
      <!-- 上传按钮 -->
      <el-icon v-if="listType === 'picture-card'" class="avatar-uploader-icon"><plus /></el-icon>
      <!-- <el-button v-if="listType === 'text'" type="primary">上传文件</el-button> -->
      <el-button v-if="listType === 'picture'" type="primary">上传文件</el-button>
      <span v-if="listType === 'text'" class="click-upload plain-text">点击上传</span>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip upload-tip" v-if="showTip">
      <template v-if="fileType">
        支持 <b style="color: #f56c6c">{{ fileType.join("/") }}</b>格式上传，
      </template>
      <template v-if="fileSize">
        大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
    </div>
    <el-image-viewer
      v-if="dialogVisible"
      :url-list="[dialogImageUrl]"
      @close="closeImg">
    </el-image-viewer>
  </div>
</template>

<script setup>
// import { getToken } from "@/utils/auth";
// import { commonUpload } from "@/api/oss"; // 运营平台原上传工具，暂时不使用
import { commonUpload, previewSingle } from "@/utils/commonUpload.js";

const props = defineProps({
  modelValue: [String, Object, Array],
  // 图片数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  listType: {  //文件列表的类型
    type: String,
    default: "picture-card",
  },
  tips:{
    type:String,
    default:'文件'
  },
  // 是否为敏感文件 ‘’ 为否  ‘PRIVATE’ 为是
  isPrivate:{
    type:String,
    default:''
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  disabledVal:{
    type: Boolean,
    default: false
  },
  formRef: {
    type: Object,
    default: () => {
    }
  },
  name: {
    type: String,
    default: ''
  },
  folder:{
    type: String,
    default: 'omsImage'
  },
  customFileShow:{ // 是否使用自定义文件展示
    type: Boolean,
    default: false
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(['files', 'update:modelValue']);
const number = ref(0);
const uploadList = ref([]);
const uploadPreviewList = ref([]); 

const dialogImageUrl = ref("");
const dialogVisible = ref(false);
// const baseUrl = import.meta.env.VITE_APP_BASE_API;
const baseUrl = '';
// const uploadImgUrl = ref(import.meta.env.VITE_APP_BASE_API + "/common/upload"); // 上传的图片服务器地址
// const headers = ref({ Authorization: "Bearer " + getToken() });
const fileList = ref([]);
const showTip = computed(
    () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(() => props.modelValue, val => {
  if (val) {
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : props.modelValue.split(",");
    // 然后将数组转为对象数组
    fileList.value = list.map(item => {
      if (typeof item === "string") {
        if (item.indexOf(baseUrl) === -1) {
          item = { name: baseUrl + item, url: baseUrl + item };
        } else {
          item = { name: item, url: item };
        }
      }
      return item;
    });
  } else {
    fileList.value = [];
    return [];
  }
},{ deep: true, immediate: true });


/**
 * 自定义图片上传
 *
 * @param options
 */
async function uploadFile(options){
  proxy.$modal.loading("正在上传文件，请稍候...");
  // await commonUpload(options.file,props.folder,props.isPrivate).then(res => {
    await commonUpload(options.file, 'image').then(async res => {
      if(res.res.status === 200){
        number.value++;
        // 解析url对象，这是一个序列化后的对象，包含bucket、fileName、originalFileName
        const fileObj = JSON.parse(res.url);

        // default fileObj {bucket: 'yt-oxms-uat', fileName: 'yt-oxms-uat/image/7a83ab47-46cf-47a9-b729-3cc592124a1d.png', originalFileName: 'Fondo-de-Ubuntu-22.04.png'}
        //public-read fileObj   {bucket: 'yt-oxms-read-uat', fileName: 'https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.…at/image/ef3ea8ea-acb9-465f-b867-ba477c9e3d3e.jpg', originalFileName: 'window.7.jpg'}
        const file = await previewSingle(fileObj.bucket, fileObj.fileName, fileObj.originalFileName);
        // default file  {bucket: 'yt-oxms-read-uat', name: 'Fondo-de-Ubuntu-22.04.png', url: 'https://yt-oxms-uat.oss-cn-shanghai.aliyuncs.com/y…FwNCDcJ2FT&Signature=ytbSTd1/SJ/4VdjcyiazheyafaM='}
        // public-read file {bucket: 'yt-oxms-read-uat', name: 'Fondo-de-Ubuntu-22.04.png', url: 'https://yt-oxms-read-uat.oss-cn-shanghai.aliyuncs.…at/image/d24b8933-6740-491e-8ea8-f23d2082d9a5.png'}
        console.log(file.url)
        fileuploadPreviewListPreviewList.value.push(file)
        uploadList.value.push(fileObj);
        // uploadList.value.push({ name: res.name, url: res.url });
        uploadedSuccessfully();
        props.formRef.validateField(props.name)
      }else{
        proxy.$modal.msgError(res.msg);
        number.value--;
        // proxy.$refs.fileUpload.handleRemove(file);
        props.formRef.validateField(props.name);
        uploadedSuccessfully();
      }
  }).finally(() => {
    proxy.$modal.closeLoading();
  })

}
// 上传前loading加载
function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = "";
    if (file.name.lastIndexOf(".") > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
    }
    isImg = props.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf("image") > -1;
  }
  if (!isImg) {
    proxy.$modal.msgError(
        `文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`
    );
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  // proxy.$modal.loading("正在上传图片，请稍候...");
  // number.value++;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  // if (res.code === 200) {
  //   uploadList.value.push({ name: res.fileName, url: res.fileName });
  //   uploadedSuccessfully();
  // } else {
  //   number.value--;
  //   proxy.$modal.closeLoading();
  //   proxy.$modal.msgError(res.msg);
  //   proxy.$refs.imageUpload.handleRemove(file);
  //   uploadedSuccessfully();
  // }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map(f => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1);
    emit("update:modelValue", listToString(fileList.value));
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    emit("update:modelValue", listToString(fileList.value));
    emit('files',fileList.value)
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError("上传图片失败");
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}
// 关闭
function closeImg(){
  dialogVisible.value = false;
}
// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (undefined !== list[i].url && list[i].url.indexOf("blob:") !== 0) {
      strs += list[i].url.replace(baseUrl, "") + separator;
    }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}

defineExpose({
  handlePictureCardPreview,
  handleDelete,
})
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
:deep(.el-upload-list){
  margin-top: 0;
}
.plain-text{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #762ADB;
  line-height: 32px;
  text-align: left;
  font-style: normal;
}
.el-upload__tip{
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 12px;
  color: #90979E;
  line-height: 12px;
  text-align: left;
  font-style: normal;
}
.upload-tip {
  position: absolute;
  bottom: -30px;
  left: 0;
  white-space: nowrap;
}
</style>
