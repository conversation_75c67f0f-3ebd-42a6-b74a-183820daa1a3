<template>
<el-row>
  <el-col :span="10">
    <el-form-item label-width="0">
      <el-select v-model="areaValue" :placeholder="$t('channel.placeholder.areaTips')" :disabled="isDisabled">
        <el-option
            v-for="(item,index) in areaList"
            :key="index"
            :label="item.label"
            :value="item.value"
        >
        </el-option>
      </el-select>
    </el-form-item>
  </el-col>
  <el-col :span="14">
    <el-form-item label-width="0" prop="mobileValue">
      <input v-model="mobileValue" :placeholder="$t('channel.placeholder.mobileNumberTips')" maxlength="16"  @blur="mobileNumberBlur" class="mobileInput" :disabled="isDisabled"/>
    </el-form-item>
  </el-col>
</el-row>
</template>
<script setup>
import {getAllCountryList} from "@/api/common.js"
const state = reactive (
    {
      areaList: [],
      form: {
        area: '',
        mobileNumber: '',
      },
    }
)
const {
  areaList,
  form
} = toRefs(state)

const props = defineProps({
  mobile: String,
  area: String,
  isDisabled: {
    type: Boolean,
    default: false
  }
});
const emit = defineEmits(['update:mobile','update:area','mobileBlur','moblieArea']);

const mobileValue = computed({
  get() {
    return props.mobile;
  },
  set(value) {
    emit('update:mobile', value);
  }
})

const areaValue = computed({
  get() {
    return props.area;
  },
  set(value) {
    emit('update:area', value);
  }
})

function mobileNumberBlur(event){
  emit('mobileBlur', event.target.value)
}

/**
 * 清楚电话号码
 */
function clearChange(){
  emit('mobileBlur', '')
}
/**
 * 区域改变
 */
function areaChange(){
  // emit('moblieArea', form.value.area)
}
/**
 * 获取区号
 */
function getAllCountryLists(){
  getAllCountryList({})
      .then((res) => {
        areaList.value = res.data.map(item => {
          return {
            label: item.internationalCode,
            value: item.internationalCode
          }
        })
        console.log(res);
      })
}
onMounted(()=>{
  getAllCountryLists();
})
</script>
<style scoped lang="scss">
.mobileInput{
  width: 100%;
  height: 100%;
  border: 1px solid #dcdfe6;
  outline: none;
}
</style>
