/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AuditRecords: typeof import('./../../../../src/components/auditRecords/index.vue')['default']
    Breadcrumb: typeof import('./../../../../src/components/Breadcrumb/index.vue')['default']
    ChangePassword: typeof import('./../../../../src/components/ChangePassword/index.vue')['default']
    CommonMobile: typeof import('./../../../../src/components/CommonMobile/index.vue')['default']
    copy: typeof import('./../../../../src/components/UploadMultiple/index copy.vue')['default']
    Crontab: typeof import('./../../../../src/components/Crontab/index.vue')['default']
    DataMarking: typeof import('./../../../../src/components/DataMarking/index.vue')['default']
    Day: typeof import('./../../../../src/components/Crontab/day.vue')['default']
    Editor: typeof import('./../../../../src/components/Editor/index.vue')['default']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTransfer: typeof import('element-plus/es')['ElTransfer']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    Empty: typeof import('./../../../../src/components/empty/index.vue')['default']
    EncryptPhone: typeof import('./../../../../src/components/EncryptPhone/index.vue')['default']
    Example: typeof import('./../../../../src/components/IconSelect/example.vue')['default']
    ExportFile: typeof import('./../../../../src/components/ExportFile/index.vue')['default']
    ExportList: typeof import('./../../../../src/components/ExportList/index.vue')['default']
    ExportSequence: typeof import('./../../../../src/components/ExportSequence/index.vue')['default']
    FileUpload: typeof import('./../../../../src/components/FileUpload/index.vue')['default']
    GoodsCard: typeof import('./../../../../src/components/GoodsCard/index.vue')['default']
    Hamburger: typeof import('./../../../../src/components/Hamburger/index.vue')['default']
    HeaderSearch: typeof import('./../../../../src/components/HeaderSearch/index.vue')['default']
    Hour: typeof import('./../../../../src/components/Crontab/hour.vue')['default']
    IconSelect: typeof import('./../../../../src/components/IconSelect/index.vue')['default']
    IEpQuestionFilled: typeof import('~icons/ep/question-filled')['default']
    IFrame: typeof import('./../../../../src/components/iFrame/index.vue')['default']
    ImagePreview: typeof import('./../../../../src/components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./../../../../src/components/ImageUpload/index.vue')['default']
    ImgPreview: typeof import('./../../../../src/components/ImgPreview/index.vue')['default']
    'Index copy': typeof import('./../../../../src/components/UploadMultiple/index copy.vue')['default']
    LangSelect: typeof import('./../../../../src/components/LangSelect/index.vue')['default']
    Min: typeof import('./../../../../src/components/Crontab/min.vue')['default']
    Month: typeof import('./../../../../src/components/Crontab/month.vue')['default']
    Pagination: typeof import('./../../../../src/components/Pagination/index.vue')['default']
    ParentView: typeof import('./../../../../src/components/ParentView/index.vue')['default']
    PreviewVideo: typeof import('./../../../../src/components/PreviewVideo.vue')['default']
    RegionCascader: typeof import('./../../../../src/components/RegionCascader/index.vue')['default']
    Result: typeof import('./../../../../src/components/Crontab/result.vue')['default']
    RightToolbar: typeof import('./../../../../src/components/RightToolbar/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Screenfull: typeof import('./../../../../src/components/Screenfull/index.vue')['default']
    Second: typeof import('./../../../../src/components/Crontab/second.vue')['default']
    SelectSupplier: typeof import('./../../../../src/components/FormColumn/SelectSupplier.vue')['default']
    SizeSelect: typeof import('./../../../../src/components/SizeSelect/index.vue')['default']
    SvgIcon: typeof import('./../../../../src/components/SvgIcon/index.vue')['default']
    TextCell: typeof import('./../../../../src/components/TextCell/index.vue')['default']
    TopNav: typeof import('./../../../../src/components/TopNav/index.vue')['default']
    TreeSelect: typeof import('./../../../../src/components/TreeSelect/index.vue')['default']
    UploadImgVideo: typeof import('./../../../../src/components/uploadImgVideo/index.vue')['default']
    UploadMultiple: typeof import('./../../../../src/components/UploadMultiple/index.vue')['default']
    UploadVideo: typeof import('./../../../../src/components/uploadVideo/index.vue')['default']
    Verify: typeof import('./../../../../src/components/verifition/Verify.vue')['default']
    VerifyPoints: typeof import('./../../../../src/components/verifition/Verify/VerifyPoints.vue')['default']
    VerifySlide: typeof import('./../../../../src/components/verifition/Verify/VerifySlide.vue')['default']
    Week: typeof import('./../../../../src/components/Crontab/week.vue')['default']
    Year: typeof import('./../../../../src/components/Crontab/year.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
