import axios from 'axios'
import { ElNotification , ElMessageBox, ElMessage } from 'element-plus'
import { getToken } from '@/utils/auth'
import useUserStore from '@/store/modules/user'
import { sign } from './sign';
import { baseConfig } from './config';
// 是否显示重新登录
export let isRelogin = { show: false };
import {getTimeZone, parseLanguage} from "@/utils";
// 创建 axios 实例
const {language, region } = parseLanguage();
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 20 * 1000
})
service.interceptors.request.use(config => {
  // config.headers['APP_ID'] = '1871121364401471490'
  config = sign(config, baseConfig.appId, baseConfig.secretKey, 'SHA256')
  if (getToken()) {
    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    config.headers['X-Time-Zone'] = getTimeZone()
    config.headers['Accept-Language'] = `${language}-${region}`
  }
  config.headers['X-Tenant-ID'] = 'T0001'
  return config
}, error => {
  console.log(error)
  Promise.reject(error)
})

// 响应拦截器
service.interceptors.response.use(async res => {
    const code = res.data.code;
    // 二进制数据则直接返回
    if (res.request.responseType ===  'blob' || res.request.responseType ===  'arraybuffer') {
      return res.data
    }
    if (code !== 0) {
      ElMessage({ message: res.data.message, type: 'error', duration: 5 * 1000 })
      return Promise.reject(new Error(res.data.message || 'Error'))
    } else {
      return Promise.resolve(res.data)
    }
  },
  error => {
    console.log('response ', error)
    let errorMessage = error.response
    ? (typeof error.response.data === 'object'
        ? error.response.data.message
        : error.response.statusText)
    : error.message


    if (!error.response) {
      ElMessage({ message: error.message, type: 'error', duration: 5 * 1000 })
    }
    else if (error.response.status === 401) {
      console.log('401', 401);
      if (!isRelogin.show) {
        isRelogin.show = true;
        ElMessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', { confirmButtonText: '重新登录', cancelButtonText: '取消', closeOnClickModal: false, type: 'warning' }).then(() => {
          useUserStore().resetToken()
          location.href = '/index';
        }).finally(() => {
          isRelogin.show = false;
        })
      }
    } else if (error.response.status === 403) {
      console.log('403', 403);

      ElNotification.error({ title: errorMessage })
    } else {
      console.log('else');
      ElMessage({ message: errorMessage, type: 'error', duration: 5 * 1000 })
    }
    return Promise.reject(error)
  }
)

export default service
