<template>
  <el-form
    class="queryParamsForm"
    :model="userForm"
    :rules="rules"
    ref="accountRef"
    label-position="top"
    label-width="80px"
  >
    <section>
      <div class="section-title">
        {{ $t("tenantsManagement.title.tenantInfo") }}
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item
            :label="$t('tenantsManagement.label.tenantName')"
          >
            <el-input
              v-model="userForm.tenantName"
              readonly
              :placeholder="$t('common.placeholder.tenantName')"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            :label="$t('tenantsManagement.label.tenantType')"
          >
            <el-select
              disabled
              v-model="userForm.tenantType"
              :placeholder="$t('common.placeholder.selectTips')"
            >
              <el-option
                v-for="item in tenantTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            :label="$t('tenantsManagement.label.validityPeriod')"
          >
            <el-date-picker
              readonly
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </section>

    <div class="section-title">
      {{ $t("tenantsManagement.title.contactInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.contacts')"
          prop="contacts"
        >
          <el-input
            v-model="userForm.contacts"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.mobile')"
          prop="mobile"
        >
          <el-input
            v-model="userForm.mobile"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { useUpdateContact } from '../composables/useUpdateContact'

const accountRef = ref(null)

const props = defineProps({
  formRef: {
    type: Object,
    required: true
  },
  tenantId: {
    type: String,
    required: true
  }
})

const {proxy} = getCurrentInstance();

const {
  userForm,
  submitLoading,
  resetForm,
  setFormData,
  handleLoad,
  handleSubmit: handleEditTenant,
  rules
} = useUpdateContact()

// 处理表单提交
const handleSubmit = async () => {
  if (!accountRef.value) return false

  try {
    await accountRef.value.validate()
    handleEditTenant()
    return true
  } catch (error) {
    return false
  }
}

// 租户类型选项
const tenantTypeOptions = computed(() => [
  {
    label: proxy.$t('tenantsManagement.label.signedTenant'),
    value: 1
  },
  {
    label: proxy.$t('tenantsManagement.label.experienceTenant'),
    value: 2
  }
])

// 日期范围
const dateRange = computed({
  get() {
    if (userForm.value.startDate && userForm.value.endDate) {
      return [userForm.value.startDate, userForm.value.endDate]
    }
    return null
  },
  set(val) {
    if (val) {
      userForm.value.startDate = val[0]
      userForm.value.endDate = val[1]
    } else {
      userForm.value.startDate = ''
      userForm.value.endDate = ''
    }
  }
})

onMounted(() => {
  handleLoad(props.tenantId)
})

defineExpose({
  userForm,
  rules,
  submitLoading,
  handleSubmit,
  resetForm,
  setFormData,
})
</script>

<style scoped lang="scss">
.section-title {
  margin: 20px 0;
  font-weight: bold;
}

:deep(.el-form-item) {
  display: flex;
  align-items: center;
}

:deep(.el-form-item__label) {
  margin-bottom: 0 !important;
}

:deep(.border-none) {
  .el-input__wrapper {
    border: none;
    box-shadow: none;
  }
  .el-select__wrapper {
    border: none;
    box-shadow: none;
  }
}
</style>
