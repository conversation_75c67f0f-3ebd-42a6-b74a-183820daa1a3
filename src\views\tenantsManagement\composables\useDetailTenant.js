import { ref } from 'vue'
import { getDetail, resetPassword } from '@/api/tenants.js'
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/index.js'
import { commonUpload, previewSingle } from "@/utils/commonUpload.js";

export function useDetailTenant() {
  const userForm = ref({
    businessLicense: "",
    contacts: "",
    mobile: "",
    startDate: "",
    endDate: "",
    systemTypes: [],
    tenantName: "",
    tenantType: null,
    unitName: "",
    uscc: "",
    account: "",
    password: ""
  })


  const businessLicensePreview = ref();

  const handleLoad = async (tenantId) => {
    try {
      const res = await getDetail({ tenantId })
      if (res.code === 0) {
        // 处理日期格式
        const processedData = {
          ...res.data,
          startDate: formatDate(res?.data?.startDate || ''),
          endDate: formatDate(res?.data?.endDate || ''),
          password: "******"
        }
        try{
          Object.assign(userForm.value, processedData)
          const fileObj = JSON.parse(processedData.businessLicense)[0]
          const file = await previewSingle(fileObj.bucket, fileObj.fileName, fileObj.originalFileName)
          businessLicensePreview.value = file
          console.log('businessLicensePreview----------', businessLicensePreview.value)
        }
        catch(e){
          console.log(e)
        }

      }
    } catch (error) {
      ElMessage.error('获取租户详情失败')
      console.error('接口调用异常:', error)
    }
  }

  return {
    userForm,
    handleLoad,
    businessLicensePreview
    // handleResetPassword
  }
}
