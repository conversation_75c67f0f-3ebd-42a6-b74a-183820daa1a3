<template>
  <div class="component-upload-image">
      <el-upload v-loading.fullscreen.lock="fullscreenLoading" multiple :disabled="disabledVal" data=""
          :list-type="listType" :on-success="handleUploadSuccess" :before-upload="handleBeforeUpload"
          :http-request="uploadFile" :limit="limit" :on-error="handleUploadError" :on-exceed="handleExceed"
          ref="imageUpload" :before-remove="handleDelete" :file-list="fileList" :on-preview="handlePictureCardPreview"
          :class="{ hide: fileList.length >= limit }">
          <template #file="{ file, index }" v-if="listType === 'picture-card'">
              <!--                <slot :file="file" :index="index">-->
              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
              <label v-if="file.status === 'success'" class="el-upload-list__item-status-label">
                  <el-icon class="el-icon-upload-success el-icon-check" color="#fff">
                      <Check />
                  </el-icon>
              </label>
              <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                      <el-icon><zoom-in /></el-icon>
                  </span>
                  <span class="el-upload-list__item-delete" v-if="isDelete" @click="handleDelete(file)">
                      <el-icon>
                          <Delete />
                      </el-icon>
                  </span>
              </span>
              <!--                </slot>-->
          </template>
          <el-icon v-if="listType === 'picture-card'" class="avatar-uploader-icon">
              <plus />
          </el-icon>
          <el-button v-if="listType === 'text'" v-show="showUploadBtn" type="primary">上传文件</el-button>
      </el-upload>
      <!-- 上传提示 -->
      <div class="el-upload__tip" v-if="showTip">
          <!--            请上传{{tips}}-->
          <template v-if="tips">
              {{ tips }}，
          </template>
          <template v-if="fileType">
              只能上传{{ fileType.join("/") }}文件，
          </template>
          <template v-if="fileSize">
              大小不超过{{ fileSize }}MB，
          </template>
          <template v-if="limit">
              最多上传{{ limit }}
              <span v-if="listType === 'text'">份</span>
              <span v-else>张</span>

          </template>
      </div>
      <el-image-viewer v-if="dialogVisible && listType === 'picture-card'" :url-list="[dialogImageUrl]"
          @close="closeImg"></el-image-viewer>
      <el-dialog v-model="dialogVisible" title="预览" width="800px" append-to-body>
          <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
      </el-dialog>
  </div>
</template>

<script setup>
import commonUpload, { previewSingle } from "@/utils/commonUpload";

const props = defineProps({
  modelValue: [String, Object, Array],
  // 是否展示删除图标
  isDelete: {
      type: Boolean,
      default: true
  },
  // 图片数量限制
  limit: {
      type: Number,
      default: 5,
  },
  // 大小限制(MB)
  fileSize: {
      type: Number,
      default: 5,
  },
  listType: {  //文件列表的类型
      type: String,
      default: "picture-card",
  },
  tips: {
      type: String,
      default: '文件'
  },
  isPrivate: {
      type: String,
      default: 'default' // default:私密， public-read:公开
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
      type: Array,
      default: () => ["png", "jpg", "jpeg"],
  },
  // 是否显示提示
  isShowTip: {
      type: Boolean,
      default: true
  },
  disabledVal: {
      type: Boolean,
      default: false
  },
  formRef: {
      type: Object,
      default: () => {
      }
  },
  name: {
      type: String,
      default: ''
  },
  showUploadBtn: {
      type: Boolean,
      default: true
  }
});

const fullscreenLoading = ref(false);
const emit = defineEmits(['files', 'update:modelValue']);
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);
watch(() => props.modelValue, async val => {
  if (val) {
      console.log("watch props.modelValue----", typeof val, val)
      // 首先将值转为数组
      const list = Array.isArray(val) ? val : JSON.parse(props.modelValue);
      // 处理数组项
      const processedList = [];
      for (const item of list) {
          // if (typeof item === "string") {
          try {
              // const fileInfo = JSON.parse(item);
              const fileInfo = item;
              const previewRes = await previewSingle(
                  fileInfo.bucket,
                  fileInfo.fileName,
                  fileInfo.originalFileName
              );
              processedList.push({
                  name: fileInfo.originalFileName,
                  url: previewRes?.url || fileInfo.fileName, // 优先使用预览链接
                  rawInfo: item // 保存原始信息
              });
              // 如果是私密文件，获取预览链接
              /* if (props.isPrivate === 'default') {
                  try {
                      const previewRes = await previewSingle(
                          fileInfo.bucket,
                          fileInfo.fileName,
                          fileInfo.originalFileName
                      );
                      processedList.push({
                          name: fileInfo.originalFileName,
                          url: previewRes?.url || fileInfo.fileName, // 优先使用预览链接
                          rawInfo: item // 保存原始信息
                      });
                  } catch (e) {
                      // 预览获取失败时使用原始链接
                      processedList.push({
                          name: fileInfo.originalFileName,
                          url: fileInfo.fileName,
                          rawInfo: item
                      });
                  }
              } else {
                  // 公共文件直接使用原始链接
                  console.log('watch fileInfo-------', fileInfo)
                  processedList.push({
                      name: fileInfo.originalFileName,
                      url: fileInfo.fileName,
                      rawInfo: item
                  });
              } */
          } catch (e) {
              // 如果不是JSON字符串，则按普通URL处理
              processedList.push({
                  name: item,
                  url: item,
                  rawInfo: item
              });
          }
          /*  } else {
               processedList.push(item);
           } */
      }
      fileList.value = processedList;
  } else {
      fileList.value = [];
  }
}, { deep: true, immediate: true });

async function uploadFile(options) {
  fullscreenLoading.value = ElLoading.service({
      lock: true,
      text: '正在上传文件，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)',
  })

  try {
      const res = await commonUpload(options.file, 'image', props.isPrivate);
      if (res.res.status === 200) {
          number.value++;
          const fileInfo = JSON.parse(res.url);
          uploadList.value.push({
              name: fileInfo.originalFileName,
              url: fileInfo.fileName,
              rawInfo: res.url // 保存完整的原始信息
          });
          console.log('----------commonUpload uploadList.value--------', uploadList.value)
          uploadedSuccessfully();
          props.formRef?.validateField(props.name);
      } else {
          ElMessage.error(res.msg);
          number.value--;
          props.formRef?.validateField(props.name);
          uploadedSuccessfully();
      }
  } catch (error) {
      ElMessage.error('上传失败');
  } finally {
      fullscreenLoading.value.close();
  }
}

// 修改文件预览处理
function handlePictureCardPreview(file) {
  if (props.isPrivate === 'default') {
      try {
          const fileInfo = typeof file.rawInfo === 'string' ?
              JSON.parse(file.rawInfo) : file.rawInfo;

          // 获取文件扩展名
          const fileExt = fileInfo.originalFileName.split('.').pop().toLowerCase();

          // 调用预览接口获取预览地址
          previewSingle(
              fileInfo.bucket,
              fileInfo.fileName,
              fileInfo.originalFileName
          ).then(res => {
              if (!!res) {
                  // 图片格式
                  if (['png', 'jpg', 'jpeg', 'gif'].includes(fileExt)) {
                      dialogImageUrl.value = res.url;
                      dialogVisible.value = true;
                  }
                  // PDF格式
                  else if (fileExt === 'pdf') {
                      window.open(res.url, '_blank');
                  }
                  // 其他格式直接下载
                  else {
                      const link = document.createElement('a');
                      link.href = res.url;
                      link.download = fileInfo.originalFileName;
                      document.body.appendChild(link);
                      link.click();
                      document.body.removeChild(link);
                  }
              } else {
                  ElMessage.error('文件预览失败');
              }
          }).catch(() => {
              ElMessage.error('文件预览失败');
          });
      } catch (e) {
          ElMessage.error('文件预览失败');
      }
  } else {
      // 公共文件，直接使用 url
      try {
          // 从原始信息中获取文件信息
          const fileInfo = typeof file.rawInfo === 'string' ?
              JSON.parse(file.rawInfo) : file.rawInfo;
          console.log('fileInfo-------', fileInfo)
          const fileExt = fileInfo.originalFileName.split('.').pop().toLowerCase();

          if (['png', 'jpg', 'jpeg', 'gif'].includes(fileExt)) {
              dialogImageUrl.value = file.url;
              dialogVisible.value = true;
          } else if (fileExt === 'pdf') {
              window.open(file.url, '_blank');
          } else {
              const link = document.createElement('a');
              link.href = file.url;
              link.download = fileInfo.originalFileName;
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
          }
      } catch (e) {
          // 如果无法解析原始信息，尝试直接使用文件URL
          window.open(file.url, '_blank');
      }
  }
}
// 修改数据转换方法
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
      if (list[i].rawInfo) {
          strs += list[i].rawInfo + separator;
      }
  }
  return strs != "" ? strs.substr(0, strs.length - 1) : "";
}
// 上传前loading加载
function handleBeforeUpload(file) {
  let isValidType = false;
  if (props.fileType.length) {
      let fileExtension = "";
      if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1).toLowerCase();
      }
      isValidType = props.fileType.some(type => {
          const lowerType = type.toLowerCase();
          return fileExtension === lowerType;
      });
  }

  if (!isValidType) {
      ElMessage.error(
          `文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`
      );
      return false;
  }

  if (props.fileSize) {
      const isLt = file.size / 1024 / 1024 < props.fileSize;
      if (!isLt) {
          ElMessage.error(`上传文件大小不能超过 ${props.fileSize} MB!`);
          return false;
      }
  }
  return true;
}
// 文件个数超出
function handleExceed() {
  ElMessage.error(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  // if (res.code === 200) {
  //   uploadList.value.push({ name: res.fileName, url: res.fileName });
  //   uploadedSuccessfully();
  // } else {
  //   number.value--;
  //   proxy.$modal.closeLoading();
  //   proxy.$modal.msgError(res.msg);
  //   proxy.$refs.imageUpload.handleRemove(file);
  //   uploadedSuccessfully();
  // }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map(f => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
      fileList.value.splice(findex, 1);
      emit("update:modelValue", listToString(fileList.value));
      return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
      fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
      console.log('===fileList===' + fileList.value);
      uploadList.value = [];
      number.value = 0;

      // emit("update:modelValue", listToString(fileList.value));
      emit("update:modelValue", rawInfoToArr(fileList.value));
      emit('files', fileList.value)
      // proxy.$modal.closeLoading();
      fullscreenLoading.value.close()
  }
}
function rawInfoToArr(rawInfo) {
  let arr = [];
  rawInfo.forEach(item => {
      arr.push(JSON.parse(item.rawInfo))
  })
  return arr;
}
// 上传失败
function handleUploadError() {
  ElMessage.error("上传图片失败");
  fullscreenLoading.value.close()
}
/**
* 下载文件
*/
function downloadFile(file) {
  window.open(file.url)
}

// 关闭
function closeImg() {
  dialogVisible.value = false;
}
defineExpose({
  handlePictureCardPreview,
  handleDelete,
})
</script>

<style scoped lang="scss">
:deep(.hide .el-upload--picture-card) {
  display: none;
}

.el-upload__tip {
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #90979E;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}
</style>
