<template>
  <el-form ref="shortForm" :model="data.loginForm" :rules="data.loginRules" class="login-form" auto-complete="on" label-position="left">
    <el-form-item prop="userName">
      <el-input
        v-model="data.loginForm.userName"
        name="userName"
        tabindex="1"
        auto-complete="on"
        maxlength="20"
        :placeholder="$t('login.mobile')"
        @keyup.enter.native="openVerify"
        
      >
        <template #prepend>
          <el-input
             class="custom-select"
       v-model="data.countryNumCode"
       style="width: 80px;"       
       disabled   

        
      ></el-input>
          <!-- <el-select
            class="custom-select"
            v-model="data.countryNumCode"
            style="width: 80px; background: white"       
            disabled     
          >
            <el-option
              v-for="item in data.countryNumCodeList"
              :key="item.id"
              :label="item.internationalCode"
              :value="item.internationalCode"
            />
          </el-select> -->
        </template>
      </el-input>
    </el-form-item>
    <el-form-item prop="password">
      <el-input ref="password" v-model="data.loginForm.password" type="password" show-password :placeholder="$t('login.password')"
      name="password" tabindex="2" auto-complete="on" @keyup.enter.native="openVerify" />
    </el-form-item>
    <el-button class="submit-button" :loading="data.loading" type="primary" @click.native.prevent="openVerify">{{ $t('login.login') }}</el-button>
    <div style="padding-top: 12px; text-align: right;">
      <span @click="goResetPwd" class="forgot">忘记密码</span>
    </div>
  </el-form>
</template>

<script setup>
import useUserStore from '@/store/modules/user'
import { useRouter } from 'vue-router'
import { getAllCountry} from '@/api/organization.js'

const userStore = useUserStore()
const router = useRouter()
const currentRoute = useRoute()
const {proxy} = getCurrentInstance()
const emit = defineEmits(['submit'])

const shortForm = ref(null)

let showVerify = inject('showVerify')
let loginSuccess = inject('loginSuccess')
const data = reactive({
	countDown: '',
	loginForm: {
		userName: '',
		password: ''
	},
	loginRules: {
		userName: [
			{ required: true, trigger: 'blur', message: proxy.$t('login.message.mobile.required') },
			// {
			// 	pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
			// 	message: '请输入正确的手机号',
			// 	trigger: ['blur']
			// }
		],
    password: [{ required: true, trigger: 'blur', message: proxy.$t('login.message.password.required') }]
	},
	loading: false,
	passwordType: 'password',
	redirect: undefined,
  countryNumCode:'+86',
  countryNumCodeList:[]
})

watch(currentRoute, (newRoute) => {
  data.redirect = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

// let timer;
// onBeforeUnmount(() => {
// 	clearInterval(timer);
// 	timer = null;
// })
let captchaVO;
function onSuccessVerify(params) {
	let captchaVO = {
		...params,
	}

	shortForm.value.validate(valid => {
		if (!valid) return
		data.loading = true
    let loginParams = {
    ...data.loginForm,
  }
  loginParams.userName =
  data.countryNumCode + "-" + data.loginForm.userName;
		emit('submit', {
			...loginParams,
			captchaVO,
		})
    userStore.login({
      loginType: 'mobilePassword',
	  systemType:"platform",
      ...loginParams,
      captchaVO,
    }).then(() => {
      loginSuccess()
    }).catch(() => {
      data.loading = false;
    });
	})
}


// function onSuccessVerify(params) {
// 	console.log(params);
// 	captchaVO = {
// 		...params,
// 	}
// 	const TIME_COUNT = 60
// 	sendMessageCode({
//     mobile: data.countryNumCode + data.loginForm.userName,
//     // captchaVO,
//   }).finally(() => data.loading = false)
//   .then(res => {
//     if (!timer) {
//       data.countDown = TIME_COUNT;
//       timer = setInterval(() => {
//         if (data.countDown > 0 && data.countDown <= TIME_COUNT) {
//           data.countDown--;
//         } else {
//           clearInterval(timer);
//           timer = null;
//         }
//       }, 1000)
//     }
//     $vm.$message.success('发送成功')
//   })
// }

function openVerify() {
	shortForm.value.validate(valid => {
		if (!valid) return;
		showVerify()
	})
}


// 获取区号
function getAreaList() {
    getAllCountry().then((res) => {
        if (res.code === 0) {
            data.countryNumCodeList = res.data;
        }
    })
}

// function handleLogin() {
// 	if (!captchaVO) {
// 		openVerify()
// 		return
// 	}
// 	shortForm.value.validate(valid => {
// 		if (!valid) return;
// 		data.loading = true
//     userStore.login({
//       loginType: 'mobile',
//       ...data.loginForm,
//       // captchaVO,
//     }).then(() => {
//       loginSuccess()
//     }).catch(() => {
//       data.loading = false;
//     });
// 	})
// }

function goResetPwd() {
	ElMessage({
    message: proxy.$t('login.contactAdmin'),
    type: 'warning',
  })
}

function resetData() {
  shortForm.value.clearValidate();
  data.loginForm.userName = "";
  data.loginForm.password = "";
  data.countryNumCode = '+86';
}

onMounted(() => {
  getAreaList();
});
defineExpose({
  onSuccessVerify,
  resetData
})
</script>

<style lang="scss" scoped>
@import "../common.scss";

::v-deep .custom-select .el-select__wrapper {
  background: #fff !important;
  height: 50px;
}
::v-deep .custom-select .el-input__validateIcon {
  display: none !important;
}

.login-form {

  ::v-deep .el-input-group__prepend{
    padding: 0 !important;
  }

  // /deep/ .countDown {
  //   height: 39px;
  //   display: inline-block;
  //   min-width: 80px;
  //   font-family: PingFangSC, PingFang SC;
  //   font-weight: 600;
  //   font-size: 16px;
  //   color: #FF7D21;
  //   line-height: 22px;
  //   border: 0;
  //   border-bottom: 1px solid #EAEAEA;
  //   padding-bottom: 17px !important;
  //   text-align: center;
  //   border-radius: 0;
  //   box-sizing: border-box;
  //   color: var(--color-primary) !important;
  //   margin-left: 0;
  //   &:hover {
  //     border-bottom: 1px solid #EAEAEA;
  //   }
  //   &:active, &:focus {
  //     border-bottom: 1px solid #EAEAEA !important;
  //     border-color: #EAEAEA !important;
  //   }
  // }
}
</style>
