<template>
  <el-dialog v-model="data.isVisible">
    <span v-html="data.videoUrl"></span>
  </el-dialog>
</template>

<script setup>
import { computed } from 'vue';

let data = reactive({
  isVisible: false,
  videoUrl: null,
})


function open(url) {
  data.isVisible = true
  data.videoUrl = `<video controls autoplay style="width: 100%;height:380px;"><source src="${url}" type="video/mp4"></video>`;
}

function close() {
  data.isVisible = false
  data.videoUrl = null
}

defineExpose({
  open,
})
</script>
