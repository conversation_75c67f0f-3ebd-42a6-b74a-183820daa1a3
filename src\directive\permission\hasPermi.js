/*
 * @Author: ch<PERSON><PERSON> <EMAIL>
 * @Date: 2025-02-24 09:32:48
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-03-07 10:06:10
 * @FilePath: \supply-operation-web\src\directive\permission\hasPermi.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
 /**
 * v-hasPermi 操作权限处理
 */

import useUserStore from '@/store/modules/user'

export const hasPermi = {
  mounted(el, binding, vnode) {
    const { value } = binding
    const all_permission = "*:*:*";
    const permissions = useUserStore().permissions
    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      const hasPermissions = permissions.some(permission => {
        return all_permission === permission || `ACTION_${permissionFlag}`.includes(permission)
      })

      if (!hasPermissions) {
        if(vnode.type == 'div'){
          el.setAttribute('style', 'pointer-events: none;');
        }else{
          el.parentNode && el.parentNode.removeChild(el)
        }
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  }
}

export const hasPermiEye = {
  mounted(el, binding, vnode) {
    const { value } = binding
    const all_permission = "*:*:*";
    const permissions = useUserStore().permissions
    if (value && value instanceof Array && value.length > 0) {
      const permissionFlag = value

      const hasPermissions = permissions.some(permission => {
        return all_permission === permission || `ACTION_${permissionFlag}`.includes(permission)
      })

      if (!hasPermissions) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    } else {
      throw new Error(`请设置操作权限标签值`)
    }
  }
}
