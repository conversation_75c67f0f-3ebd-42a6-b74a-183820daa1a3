import { querySupplierList } from "@/api/common.js";

export function useMainData() {
  const { proxy } = getCurrentInstance()
  console.log('proxy', proxy);
  let mainState = reactive({
    mainTable: [
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: 'Tom', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: 'Tom', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: 'Tom', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: 'Tom', address: 'No. 189, Grove St, Los Angeles', },
    ],
    mainTableTotal: 0,
    mainTableLoading: false,
    mainParams: initMainParams(),
  })

  let { mainTable, mainTableLoading, mainTableTotal, mainParams } = toRefs(mainState)

  const getMainTable = () => {
    // mainTableLoading.value = true
    // querySupplierList(mainParams.value).then(res => {
    //   mainTable.value = res.data.records
    //   mainTableTotal.value = res.data.total * 1
    // }).finally(() => mainTableLoading.value = false)
  }

  function initMainParams () {
    return {
      page: 1,
      limit: 10,
    }
  }

  function resetMainParams() {
    mainParams.value = initMainParams()
    handleMainQuery()
  }

  function handleMainQuery() {
    mainParams.value.page = 1
    getMainTable()
  }


	return {
    mainState,
    mainTable,
    mainTableTotal,
    mainTableLoading,
    mainParams,
    getMainTable,
    initMainParams,
    resetMainParams,
    handleMainQuery,
	}
}


