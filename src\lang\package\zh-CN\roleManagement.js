export default {
    roleManagement:{
        operation:{
            add: '添加',
            edit: '编辑',
            enable: '启用',
            disable: '禁用',
        },
        title: {
            addRoleTitle:'新建角色',
            editRoleTitle:"编辑角色"
        },
        label: {
            roleName:'角色名称',
            roleStatus:'角色状态',
            accountNum:'账号数',
            roleDesc: '角色描述',
            status: '状态',
            createTime: '创建时间',
            operate: '操作',
            permission:'权限设置'
        },
        placeholder: {
            roleName:'请输入角色名称',
            roleNameFomart:'可输入1到20位中文、英文和数字',
            status:'请选择角色状态'
        },
        button: {
            addRole:'新建角色'
        },
        prompt: {
            inputTips: '请输入',
            selectTips: '请选择',
            uploadTips: '请上传',
            appTips: '新增成功',
            editTips: '修改成功',
            cannotEmpty: '不能为空',
            delete: {
                title: "提示",
                content: "确定要删除所选数据吗？",
            },
            change: {
                title:"提示",
                content: "确定要更新所选状态？"
            }
        },
        message: {
            deleteNotTips: '该角色处于启用状态，不能删除！',
            deleteTips: '确定要删除所选数据吗？',
            deleteSucess: '删除成功！',
            deleteFail: '删除失败！',
            disableTips: '确定要禁用当前角色吗？',
            enableSucess: '启用成功！',
            enableFail: '启用失败！',
            disableSucess: '禁用成功！',
            disableFail: '禁用失败！',
            addSucess: '添加成功',
            editSucess: '编辑成功',

        },
        rules:{
        }

    },
}
