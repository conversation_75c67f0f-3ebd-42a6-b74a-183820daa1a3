import { ref } from 'vue'
import {
  upadteTenant,
  getDetail
} from "@/api/tenants.js";
import { ElMessage } from 'element-plus'
import { commonUpload, previewSingle } from "@/utils/commonUpload.js";

export function useUpdateContact() {
  const userForm = ref({
    businessLicense: "",
    contacts: "",
    endDate: "",
    mobile: "",
    startDate: "",
    systemTypes: ["supply"],
    tenantId: "",
    tenantName: "",
    tenantType: null,
    unitName: "",
    uscc: "",
  })

  const rules = {
    contacts: [
      { required: true, message: '请输入联系人', trigger: 'blur' },
      { max: 30, message: '联系人长度不能超过30个字符', trigger: ['blur', 'change'] }
    ],
    mobile: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { max: 30, message: '手机号码长度不能超过30个字符', trigger: ['blur', 'change'] }
    ],
    /* systemTypes: [
      {
        required: true,
        message: "请选择系统类型",
        trigger: "change",
        type: 'array'
      },
    ], */
  }

  const submitLoading = ref(false)
  const businessLicensePreview = ref();
  const handleLoad = async (tenantId) => {
    try {
      const res = await getDetail({ tenantId })
      if (res.code === 0) {
        // 处理日期格式
        const processedData = {
          ...res.data,
          startDate: formatDate(res?.data?.startDate || ''),
          endDate: formatDate(res?.data?.endDate || ''),
          password: "******"
        }
        Object.assign(userForm.value, processedData)
       /*  Object.assign(userForm.value, processedData)
          const fileObj = JSON.parse(processedData.businessLicense)
        const file = await previewSingle(fileObj.bucket, fileObj.fileName, fileObj.originalFileName)
        businessLicensePreview.value = file */
      }
    } catch (error) {
      ElMessage.error('获取租户详情失败')
      console.error('接口调用异常:', error)
    }
  }

  // 格式化日期
  const formatDate = (timestamp) => {
    if (!timestamp) return ''
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 处理表单提交
  const handleSubmit = async () => {
    submitLoading.value = true
    try {
      const params = {
        tenantId: userForm.value.tenantId,
        ...userForm.value,
        contacts: userForm.value.contacts,
        mobile: userForm.value.mobile,
        startDate: (new Date(userForm.value.startDate)).getTime(),
        endDate: (new Date(userForm.value.endDate)).getTime()
      }
      const res = await upadteTenant(params)
      if (res.code === 0) {
        ElMessage.success('更新租户成功')
        return true
      }
      // ElMessage.error(res.msg || '更新租户失败')
      return false
    } catch (error) {
      console.error('Error updating tenant:', error)
      // ElMessage.error('更新租户失败')
      return false
    } finally {
      submitLoading.value = false
    }
  }

  // 重置表单
  const resetForm = () => {
    userForm.value = {
      businessLicense: "",
      contacts: "",
      endDate: "",
      mobile: "",
      startDate: "",
      systemTypes: ["supply"],
      tenantId: "",
      tenantName: "",
      tenantType: null,
      unitName: "",
      uscc: "",
    }
  }

  // 设置表单数据
  const setFormData = (data) => {
    const formData = { ...data }
    if (data.startDate && data.endDate) {
      formData.startDate = formatDate(data.startDate)
      formData.endDate = formatDate(data.endDate)
    }
    // 确保系统设置始终被选中
    if (!formData.systemTypes?.includes('supply')) {
      formData.systemTypes = [...(formData.systemTypes || []), 'supply']
    }
    userForm.value = formData
  }

  return {
    userForm,
    rules,
    submitLoading,
    handleSubmit,
    resetForm,
    setFormData,
    handleLoad,
    businessLicensePreview
  }
}
