<template>
  <div class="d2Cell">
    <span class="d2Cell-label">{{ props.label }}</span>
    <span class="d2Cell-value">
      <template v-if="props.value">{{ props.value }}</template>
      <slot v-else />
    </span>
  </div>
</template>

<script setup>
let props = defineProps({
  label: String,
  value: String,
  layout: {
    type: String,
    default: 'inline-block', //  'block' | 'inline'
  },
})

let display = computed(() => {
  if (props.layout === 'block') {
    return 'flex'
  }
  return 'inline-block'
})
</script>

<style lang="scss" scoped>
.d2Cell {
  display: v-bind(display);
  font-weight: 400;
  font-size: 14px;
  vertical-align: top;
  &-label {
    color: var(--text-color-grey);
  }
  &-value {
    color: var(--text-color);
    word-wrap: break-word;
    word-break: break-all;
  }
}
</style>
