<template>
  <div class="workbench">
    <div class="userInfo">
      <div class="user-name">
        <div class="name-font">{{ $t('workbench.title.helloTip') }}，{{ userStore.userInfo.nickName }}！</div>
        <div class="time-font">{{ currentDate }}</div>
      </div>
      <img src="@/assets/images/workbench_bg.png" class="user-bg"/>
    </div>
    <!--待办提醒-->
    <div class="wait-box">
      <div class="index-label">{{ $t('workbench.label.todoReminder') }}</div>
      <div class="todo-list">
        <div class="list-group item-bg" style="flex:3;">
          <!--          待核验订购单-->
          <div class="group-box ">
            <div class="item-num">{{ todoNum.awaitCheckPurchaseOrderNum }}</div>
            <div class="item-label">
              <span class="item-name">{{ $t('workbench.title.awaitCheckPurchaseOrder') }}</span>
              <span class="handle-item" @click="handleLink(1)">{{ $t('workbench.btn.goVerification') }}</span>
            </div>
          </div>
          <div class="item-split"></div>
          <!--          待审核采购单-->
          <div class="group-box">
            <div class="item-num">{{ todoNum.awaitAuditPurchaseOrderNum }}</div>
            <div class="item-label">
              <span class="item-name">{{ $t('workbench.title.awaitAuditPurchaseOrder') }}</span>
              <span class="handle-item" @click="handleLink(2)">{{ $t('workbench.btn.goView') }}</span>
            </div>
          </div>
          <div class="item-split"></div>
          <!--          待发货采购单-->
          <div class="group-box">
            <div class="item-num">{{ todoNum.awaitDeliveryPurchaseOrderNum }}</div>
            <div class="item-label">
              <span class="item-name">{{ $t('workbench.title.awaitDeliveryPurchaseOrder') }}</span>
              <span class="handle-item" @click="handleLink(3)">{{ $t('workbench.btn.goView') }}</span>
            </div>
          </div>
        </div>
        <div class="list-group item-bg" style="flex:2;">
          <!--        待物流订舱-->
          <div class="group-box">
            <div class="item-num">{{ todoNum.awaitBookingNum }}</div>
            <div class="item-label">
              <span class="item-name">{{ $t('workbench.title.awaitBookingSpace') }}</span>
              <span class="handle-item" @click="handleLink(7)">{{ $t('workbench.btn.goBookingSpace') }}</span>
            </div>
          </div>
          <!--        待售后-->
          <div class="item-split"></div>
          <div class="group-box">
            <div class="item-num">{{ todoNum.awaitServiceOrderNum }}</div>
            <div class="item-label">
              <span class="item-name">{{ $t('workbench.title.awaitServiceOrder') }}</span>
              <span class="handle-item" @click="handleLink(5)">{{ $t('workbench.btn.goHandle') }}</span>
            </div>
          </div>
        </div>
        <div class="list-group item-bg" style="flex:2;">
          <!--        待审核客户-->
          <div class="group-box">
            <div class="item-num">{{ todoNum.awaitAuditCustomerNum }}</div>
            <div class="item-label">
              <span class="item-name">{{ $t('workbench.title.awaitAuditCustomer') }}</span>
              <span class="handle-item" @click="handleLink(4)">{{ $t('workbench.btn.goAudit') }}</span>
            </div>
          </div>
          <!--        待收款-->
          <div class="item-split"></div>
          <div class="group-box">
            <div class="item-num">￥{{ formatNumber(todoNum.awaitPaid) }}</div>
            <div class="item-label">
              <span class="item-name">{{ $t('workbench.title.awaitPaid') }}</span>
              <span class="handle-item" @click="handleLink(6)">{{ $t('workbench.btn.goHandle') }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!--    快捷导航-->
    <div class="nav-box">
      <div class="index-label">{{ $t('workbench.label.navigation') }}</div>
      <div class="menu-box">
        <div class="menu-tag" v-for="(item) in homeMenuList" :key="item.menuId">
          <div @click="handleMenuLink(item)">{{ item.menuName }}</div>
        </div>
        <div class="add-nav-btn" @click="addNavBtn"> + {{ $t('workbench.btn.addNav') }}</div>
      </div>
    </div>
    <!--    交易概览-->
    <div class="chart-module">
      <!--    交易概览-->
      <div class="trade-info">
        <div class="index-label">{{ $t('workbench.label.tradeOverview') }}</div>
        <div class="chart-content">
          <div class="trade-total">
            <div class="trade-num">￥{{ formatNumber(tradeAmount) || 0 }}</div>
            <div class="trade-total-name">{{ $t('workbench.title.salesAmount') }}</div>
            <!--            <div class="trade-percent">100%</div>-->
          </div>
          <div class="trade-line">
            <div class="trade-query-time">
              <el-radio-group v-model="queryParams.queryType" @change="setCurrDate()" size="small">
                <el-radio-button class="item-title" type="text" label="date">{{
                    $t('workbench.btn.date')
                  }}
                </el-radio-button>
                <el-radio-button class="item-title" type="text" label="month">{{
                    $t('workbench.btn.month')
                  }}
                </el-radio-button>
                <el-radio-button class="item-title" type="text" label="year">{{
                    $t('workbench.btn.year')
                  }}
                </el-radio-button>
              </el-radio-group>
              <div class="date-picker-wrapper">
                <el-date-picker
                  v-model="queryParams.queryDate"
                  class="query-date-picker"
                  :type="queryParams.queryType"
                  value-format="x"
                  :editable="false"
                  :clearable="false"
                  :prefix-icon="customPrefix"
                  :disabled-date="disabledDate"
                  ref="datePickerRef"
                  @change="handleDateType"
                >
                </el-date-picker>
                <el-icon size="12" @click="datePickerFocus">
                  <ArrowDown/>
                </el-icon>
              </div>
            </div>
            <line-chart :id="'tradeLine'"
                        :title="$t('workbench.label.tradeLineName')"
                        :custom-chart-data="tradeChart"
                        :custom-color="'#FFC000'"
                        :line-legend="[$t('workbench.label.tradeLineUnit')]"
                        :line-style="tradeStyle"
                        :line-title-style="{left: '4%'}">
            </line-chart>
          </div>
        </div>
      </div>
      <!--    客户统计-->
      <div class="customer-info">
        <div class="index-label">{{ $t('workbench.label.customStatistics') }}</div>
        <div class="chart-content">
          <div class="custom-pie">
            <pie-chart :pieData="customerAuthData" :tooltipName="$t('workbench.label.pieToolTipName')"></pie-chart>
          </div>

          <div class="custom-line">
            <line-chart :id="'customLine'"
                        :title="$t('workbench.label.customLineName')"
                        :custom-chart-data="customerChart"
                        :custom-color="'#FF0019'"
                        :line-legend="[$t('workbench.label.customLineUnit')]"
                        :line-style="customStyle"
                        :line-title-style="{left: '32%'}"
            >
            </line-chart>
          </div>
        </div>
      </div>
    </div>

    <div class="country-info">
      <div class="index-label">
        {{ $t('workbench.label.countryInfo') }}
      </div>
      <div class="country-content">
        <div class="country-item" v-for="item in countryInfo" :key="item.id">
          <img style="width: 60px; height: auto; margin-right: 10px;" :src="item.countryFlag"/>
          <div>
            <div class="countryName">{{ item.countryName }}</div>
            <div class="supplierNum">{{ item.supplierNum }}</div>
          </div>
        </div>
      </div>
    </div>

    <AddMenu v-model="addDialog.open"
             ref="addNavRef"
             :title="addDialog.title"
             :menu-list="menuList"
             @submitted="submitMenu">
    </AddMenu>
  </div>
</template>

<script setup name="Workbench">
import AddMenu from "@/views/workbench/components/addMenu.vue";
import lineChart from "./components/lineChart.vue"
import PieChart from "./components/pieChart.vue";
import {
  customerAuthPieChart,
  salesTrends,
  customerAuthTrends,
  totalAmount,
  todoList,
  supplierCountry,
  getHomeMenuList, getAuthorityMenu
} from "@/api/workbench.js"
import useUserStore from "@/store/modules/user.js";
import {formatIndexDate, formatNumber, getYesterday} from "@/utils/index.js";

const {proxy} = getCurrentInstance()
const router = useRouter();
const {t} = useI18n();
const currentDate = ref('');
let userStore = useUserStore()
const data = reactive({
  menuList: [],
  homeMenuList: [],
  todoNum: {
    awaitAuditCustomerNum: 0,
    awaitAuditPurchaseOrderNum: 0,
    awaitCheckPurchaseOrderNum: 0,
    awaitDeliveryPurchaseOrderNum: 0,
    awaitBookingNum: 0,
    awaitPaid: 0,
    awaitServiceOrderNum: 0
  },
  tradeAmount: {},
  tradeChart: {
    xAxisData: [],
    yAxisData: [],
  },
  customerAuthData: [], // 客户类型:1:企业客户 2:政府客户 3:个体工商
  customerChart: {
    xAxisData: [],
    yAxisData: [],
  },
  countryInfo: {} // 原产地供应商
})

const {
  menuList,
  homeMenuList,
  todoNum,
  customerAuthData,
  tradeChart,
  tradeAmount,
  customerChart,
  countryInfo
} = toRefs(data)

const tradeStyle = ref({
  left: '25%', // 交易概览图例位置
})

const customStyle = ref({
  right: '2%', // 客户统计图例位置
})

const queryParams = reactive({
  queryType: 'date', //	查询时间类型 (日 date, 月 month , 年 year, 自定义 custom)
  queryDate: ''
})

const datePickerRef = ref(null)

// 时间控件自定义前缀的内容
const customPrefix = shallowRef({
  render() {
    return h('p', '')
  },
})

// 设置时间选择范围
const disabledDate = (time) => {
  return time.getTime() > Date.now() - 24 * 3600000;
}

let addDialog = ref({
  open: false,
  title: ''
})

function updateCurrentDate() {
  currentDate.value = formatIndexDate('YYYY-MM-DD hh:mm:ss', new Date());
}

let intervalId = null;

onMounted(() => {
  updateCurrentDate();
  intervalId = setInterval(updateCurrentDate, 1000); // 每秒更新一次
  // initInfo()
});

onUnmounted(() => {
  if (intervalId) {
    clearInterval(intervalId);
  }
});

function setCurrDate() {
  // 昨日时间
  let yesterday = getYesterday()
  queryParams.queryDate = formatIndexDate('YYYY-MM-DD', yesterday);
  handleDateType()
}

function initInfo() {
  setCurrDate()
  queryAuthorityMenu()
  queryTodoList()
  queryCustomerPieChart()
  queryCustomerAuthTrends()
  querySalesTrends()
  tradeTotalAmount()
  queryHomeMenuList()
  querySupplierCountry()
}

function handleDateType() {
  switch (queryParams.queryType) {
    case 'date':
      queryParams.queryDate = formatIndexDate('YYYY-MM-DD', queryParams.queryDate)
      break;
    case 'month':
      queryParams.queryDate = formatIndexDate('YYYY-MM-DD', queryParams.queryDate).substring(0, 7)
      break;
    case 'year':
      queryParams.queryDate = formatIndexDate('YYYY-MM-DD', queryParams.queryDate).substring(0, 4)
      break;
    default:
  }
  querySalesTrends()
  tradeTotalAmount()
}

function datePickerFocus() {
  datePickerRef.value.focus()
}

function handleLink(type) {
  switch (type) {
    case 1:  //待核验订购单
      router.push({path: '/order/management', query: {checkStatus: 0}})
      break;
    case 2:  //待审核采购单
      router.push({path: '/order/purchaseManagement', query: {purchaseOrderStatus: 0}})
      break;
    case 3:  //待发货采购单
      router.push({path: '/order/purchaseManagement', query: {purchaseOrderStatus: 20}})
      break;
    case 4:  //待审核客户
      router.push({path: '/channel/customerManagement', query: {authStatus: 'INIT'}})
      break;
    case 5:  //待售后
      router.push({path: '/afterSales/management', query: {serviceStatus: 0}})
      break;
    case 6:  //待收款
      router.push({path: '/finance/accountsReceivable', query: {billStatus: 0}})
      break;
    case 7: // 订舱
      router.push({path: '/logistics/airTransport', query: {bookStatus: 0}})
      break;
    default:
      break
  }
}

function handleMenuLink(item) {
  router.push(item.path)
}

let addNavRef = ref()

function addNavBtn() {
  addNavRef.value.setMenuList(homeMenuList.value)
  addDialog.value.open = true
  addDialog.value.title = t('workbench.title.setNav')
}

function queryHomeMenuList() {
  getHomeMenuList().then(res => {
    if (res.code === 0) {
      homeMenuList.value = res.data ? res.data : []
    }
  })
}

// 已分配权限的菜单列表
function queryAuthorityMenu() {
  getAuthorityMenu().then(res => {
    if (res.code === 0) {
      menuList.value = res.data.menuList
    }
  })
}

// 待办提醒
function queryTodoList() {
  todoList().then(res => {
    if (res.code === 0) {
      todoNum.value = res.data
    }
  })
}

// 客户统计：饼图
const newColors = ['#762ADB', '#FBC932', '#B36B05'];
const enterpriseType = [t('workbench.label.enterpriseType1'), t('workbench.label.enterpriseType2'), t('workbench.label.enterpriseType3')];

function queryCustomerPieChart() {
  customerAuthPieChart().then(res => {
    if (res.code === 0) {
      if (!res.data) {
        customerAuthData.value = enterpriseType.map((item, index) => {
          return {
            name: enterpriseType[index],
            value: 0,
            itemStyle: {color: newColors[index]}
          }
        })
      } else {
        customerAuthData.value = res.data.map((item, index) => {
          return {
            name: enterpriseType[parseInt(item.enterpriseAuthType) - 1],
            value: item.customerAuthNum,
            itemStyle: {color: newColors[index]}
          }
        })
      }
    }
  })
}

// 客户趋势图
function queryCustomerAuthTrends() {
  customerAuthTrends().then(res => {
    if (res.code === 0) {
      // 数据为 0
      if (res.data.customerAuthNum.length === 0) {
        let yAxisData = new Array(res.data.dates).fill(0)
        customerChart.value = {
          xAxisData: res.data.dates,
          yAxisData: yAxisData
        }
      } else {
        customerChart.value = {
          xAxisData: res.data.dates,  // 客户认证数
          yAxisData: res.data.customerAuthNum // 日期集合
        }
      }
    }
  })
}

// 交易统计趋势图
function querySalesTrends() {
  salesTrends(queryParams).then(res => {
    if (res.code === 0) {
      // 数据为 0
      if (res.data.sales.length === 0) {
        let yAxisData = new Array(res.data.dates).fill(0)
        tradeChart.value = {
          xAxisData: res.data.dates,
          yAxisData: yAxisData
        }
      } else {
        tradeChart.value = {
          xAxisData: res.data.dates,// 日期集合
          yAxisData: res.data.sales // 销售额
        }
      }
    }
  })
}

// 销售金额
function tradeTotalAmount() {
  totalAmount(queryParams).then(res => {
    if (res.code === 0) {
      tradeAmount.value = res.data
    }
  })
}

// 原产地供应商
function querySupplierCountry() {
  supplierCountry().then(res => {
    if (res.code === 0) {
      countryInfo.value = res.data
    }
  })
}

// submitMenu
function submitMenu() {
  queryHomeMenuList()
}

</script>

<style scoped lang="scss">
.workbench {
  background: #F2F3F4;
  //height: 100vh;

  .userInfo {
    background: #FFFFFF;
    padding: 10px;
    display: flex;
    align-items: center;

    .user-name {
      margin-left: 14px;
      align-content: center;
      background: linear-gradient(to right, #FFFFFF 0%, #FFFDFB 100%);
      flex: 1;
      height: 60px;
    }

    .user-bg {
      height: 60px;
    }
  }

  .name-font {
    font-family: DingTalk;
    font-weight: normal;
    font-size: 16px;
    color: #252829;
    line-height: 19px;
    text-align: left;
    font-style: normal;
  }

  .time-font {
    margin-top: 4px;
    font-weight: 400;
    font-size: 12px;
    color: #C8C9CC;
    line-height: 16px;
    font-style: normal
  }

  .wait-box {
    background: #FFFFFF;
    padding: 16px 20px 20px 20px;
    margin-top: 10px;

    .todo-list {
      display: flex;
      align-items: center;

      .list-group {
        display: flex;
        align-items: center;
        justify-content: space-between;
        //flex: 1;

        &:nth-child(n+2) {
          margin-left: 12px;
        }
      }

      .group-box {
        padding: 16px;
        flex: 1;
      }

      .item-split {
        content: ' ';
        display: inline-block;
        width: 1px;
        height: 20px;
        background: #E6E8EC;
      }
    }

    .item-bg {
      background: #FBFBFB;
      border-radius: 4px;
      border: 1px solid #EDEFF2;
    }

    .item-box {
      padding: 16px;
      background: #FBFBFB;
      border-radius: 4px;
      border: 1px solid #EDEFF2;
      //flex: 1;

      &:nth-child(n+2) {
        margin-left: 12px;
      }
    }

    .item-num {
      font-family: 'DINAlternate';
      font-weight: bold;
      font-size: 28px;
      color: #252829;
      line-height: 32px;
      //overflow-wrap: break-word;
      //white-space: pre-wrap;
      //word-break: break-word;
    }

    .item-label {
      margin-top: 6px;
    }

    .item-name {
      font-weight: 400;
      font-size: 14px;
      color: #252829;
      line-height: 20px;
      min-width: 90px;
      display: block;
    }

    .handle-item {
      font-weight: 400;
      font-size: 14px;
      color: #762ADB;
      line-height: 20px;
      min-width: 36px;
      cursor: pointer;
      display: block;
      margin-top: 6px;
    }
  }

  .index-label {
    font-weight: 500;
    font-size: 14px;
    color: #252829;
    line-height: 20px;
    margin-left: 5px;
    margin-bottom: 16px;

    &:before {
      content: ' ';
      background: #762ADB;
      display: inline-block;
      width: 3px;
      height: 12px;
      position: relative;
      top: 1px;
      right: 4px;
    }
  }

  .nav-box {
    background: #FFFFFF;
    padding: 16px 24px 20px 24px;
    margin-top: 10px;

    .menu-box {
      display: flex;
      flex-wrap: wrap;
      align-items: center;

      .menu-tag {
        margin-right: 12px;
        margin-bottom: 6px;
        background: #FBFBFB;
        border-radius: 4px;
        border: 1px solid #EDEFF2;
        text-align: center;
        padding: 10px 16px;
        font-weight: 500;
        font-size: 14px;
        color: #323233;
        line-height: 20px;
        cursor: pointer;
      }

      .add-nav-btn {
        background: rgba(192, 12, 29, 0.06);
        border-radius: 4px;
        border: 1px solid #762ADB;
        font-weight: 400;
        font-size: 14px;
        color: #762ADB;
        line-height: 20px;
        padding: 10px 16px;
        cursor: pointer;
        margin-bottom: 6px;
      }
    }
  }

  .chart-module {
    display: flex;
    margin-top: 10px;

    .chart-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
    }

    .trade-info {
      background: #FFFFFF;
      padding: 16px 24px 20px 24px;
      flex: 1;

      .trade-total {
        background: #FBFBFB;
        border-radius: 4px;
        border: 1px solid #EDEFF2;
        padding: 40px 14px 44px 14px;

        .trade-num {
          font-family: 'DINAlternate';
          font-weight: 600;
          font-size: 26px;
          color: #252829;
          line-height: 32px;
          min-width: 130px;
          max-width: 184px;
          overflow-wrap: break-word;
          white-space: pre-wrap;
          word-break: break-word;
        }

        .trade-total-name {
          font-weight: 400;
          font-size: 16px;
          color: #252829;
          line-height: 20px;
          margin: 10px 0 20px 4px;
        }

        .trade-percent {
          font-weight: 500;
          font-size: 14px;
          color: #1ACB42;
          line-height: 20px;
        }
      }

      .trade-line {
        width: 60%;
        flex: 2;
        position: relative;
      }

      .trade-query-time {
        display: flex;
        justify-content: space-around;
        width: 52%;
        position: absolute;
        right: -14px;
        top: 6px;
        z-index: 1;
      }
    }

    .customer-info {
      background: #FFFFFF;
      padding: 16px 18px 20px 14px;
      margin-left: 10px;
      flex: 1;

      .custom-line {
        flex: 2;
      }
    }
  }

  .country-info {
    margin-top: 10px;
    background: #FFFFFF;
    padding: 16px 24px 20px 24px;

    .country-content {
      display: flex;
      align-items: center;

      .country-item {
        width: 200px;
        padding: 15px 20px;
        background: #FBFBFB;
        border-radius: 4px;
        border: 1px solid #EDEFF2;
        display: flex;

        .countryName {
          font-weight: 500;
          font-size: 14px;
          color: #252829;
          line-height: 20px;
          margin-bottom: 2px;
        }

        .supplierNum {
          font-weight: 400;
          font-size: 12px;
          color: #323233;
          line-height: 16px;
        }

        &:nth-child(n+2) {
          margin-left: 12px;
        }
      }
    }
  }
}

.trade-info {
  :deep(.el-input__wrapper) {
    border-radius: 0;
    border: 0;
    padding-left: 0;
    padding-right: 0;
    box-shadow: none !important;
    vertical-align: middle;
  }
}

:deep(.el-date-editor) {
  width: 76px !important;
}

.date-picker-wrapper {
  display: flex;
  align-items: center;
}

:deep(.el-input__prefix-inner) {
  width: 2px;
}

:deep(.el-dialog__footer) {
  padding-right: 16px;
  box-shadow: inset 0px 1px 0px 0px #ECEEF1;
}

:deep(.el-dialog) {
  padding: 16px 0;
}

:deep(.el-dialog__header) {
  padding-left: 16px;
  box-shadow: inset 0px -1px 0px 0px #ECEEF1;
}

:deep(.el-dialog__body) {
  padding: 16px;
}
</style>

