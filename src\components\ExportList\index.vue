<template>
  <slot :waitDownLoadCount="data.waitDownLoadCount">
    <el-button v-resubmit type="primary" plain @click="addDownloadItem">{{ $t('common.download')  }}</el-button>
    <el-button @click="showDialog">下载序列({{ data.waitDownLoadCount }})</el-button>
  </slot>
  <span class="">
    <el-dialog title="下载序列" v-model="data.exportVisible" width="900px">
      <el-table :data="data.exportData" v-loading="data.tableLoading" border>
        <el-table-column property="fileName" label="文件名" align="center"></el-table-column>
        <el-table-column property="date" label="创建时间" align="center">
          <template #default="subs">
            {{ parseDateTime(subs.row.date, 'dateTime') }}
          </template>
        </el-table-column>
        <el-table-column property="status" label="状态" align="center">
          <template #default="subs">
            <div style="font-size: 12px;"
              :class="{ 'gray': subs.row.status === 0, 'green': subs.row.status === 1, 'red': subs.row.status === 2, 'blue': subs.row.status === 3, }">
              {{ statusFilters(subs.row.status) }}</div>
          </template>
        </el-table-column>
        <el-table-column property="address" label="操作" align="center">
          <template #default="subs">
            <span v-if="subs.row.status === 1" class="text-primary"
              @click="download(subs.row)">{{ $t('common.download') }}</span>
            <span v-else> - </span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </span>
</template>
<script setup>
import * as utils from '@/utils/index';
import { queryExportList, updateExportItemStatus } from "@/api/common";
import request from '@/utils/request'
import { onUnmounted } from 'vue';

const { proxy } = getCurrentInstance()

const props = defineProps({
  path: {
    type: String,
    required: true
  },

  requestUrl: {
    type: String,
    required: true
  },

  requestParams: {
    type: null,
  },
})

const data = reactive({
  exportData: [],
  exportVisible: false,
  tableLoading: false,
  waitDownLoadCount: 0
})

onMounted(() => {
  getExportList()
})

function getExportList() {
  data.tableLoading = true
  queryExportList({ path: props.path }).then(res => {
    data.exportData = res.data.downLoadList
    data.waitDownLoadCount = res.data.waitDownLoadCount
    data.tableLoading = false
  })
    .catch(err => {
      data.tableLoading = false
      proxy.$message.error(err.message);
    });
}

function showDialog() {
  data.exportVisible = true
  getExportList();
}

// 增加下载序列数据
function addDownloadItem() {
  request({
    url: props.requestUrl,
    method: 'post',
    data: props.requestParams
  }).then(res => {
    proxy.$confirm('可在下载序列中查看进度并获取下载文件', '已创建下载任务', {
      confirmButtonText: '查看',
      cancelButtonText: '取消',
      type: 'success'
    }).then(() => {
      showDialog()
      refreshExport()
    }).catch(() => {
      refreshExport()
    });
  })

}

// 下载序列文件
function download(obj) {
  updateExportItemStatus({
    path: props.path,
    queryId: obj.queryId
  }).then(res => {
    utils.downloadFile(obj.filePath);
    getExportList()
  }).catch(err => {
    proxy.$message.error(err.message);
  });
}

let interval
function refreshExport() {
  let i = 0
  interval = setInterval(function () {
    if (i < 5) {
      getExportList()
      i++
    } else {
      clearInterval(interval)
    }
  }, 5000)
}

onUnmounted(() => {
  clearInterval(interval)
  interval = null
})

function statusFilters(status) {
  const serviceState = {
    0: "下载中",
    1: "下载成功",
    2: "下载失败",
    3: "已下载",
  };
  return serviceState[status];
}

defineExpose({
  addDownloadItem,
  showDialog,
})
</script>
<style lang="scss" scoped>
.green {
  color: green;
}

.red {
  color: red;
}

.gray {
  color: gray;
}

.blue {
  color: blue;
}


.text-primary {
  color: var(--color-primary);
  font-size: 12px;
  cursor: pointer;
}
</style>
