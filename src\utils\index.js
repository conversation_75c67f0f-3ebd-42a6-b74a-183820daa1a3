import Big from 'big.js';
import { dayjs } from 'element-plus'

export function getAssetsUrl(path) {
  return new URL(`../assets/images/${path}`, import.meta.url).href
}


/**
 * 解析OSS链接
 * @param {string} url - OSS链接
 **/
export function parseOSSUrl(url) {

  const parsedUrl = new URL(url);

  // 获取 bucket 名称（主机名的第一部分）
  const bucket = parsedUrl.hostname.split('.')[0];

  // 获取路径部分（去除开头的斜杠）
  const ossPath = parsedUrl.pathname.substring(1);

  // 获取解码后的路径
  const fileName = decodeURIComponent(ossPath);

  // 提取文件名（路径中的最后一部分）
  const originalFileName = fileName.substring(fileName.lastIndexOf('/') + 1);

  return {
    bucket,
    ossPath,
    fileName,
    originalFileName
  };
}

// 日期格式化
export function parseTime(time, pattern) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = pattern || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
      time = parseInt(time)
    } else if (typeof time === 'string') {
      time = time.replace(new RegExp(/-/gm), '/').replace('T', ' ').replace(new RegExp(/\.[\d]{3}/gm), '');
    }
    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    if (result.length > 0 && value < 10) {
      value = '0' + value
    }
    return value || 0
  })
  return time_str
}
const replaceStr = (str,index,char,symbol = '') => {
  let strArr = str.split(symbol)
  strArr[index] = char
  return strArr.join(symbol)
}
// 时区转换
export function parseDateTime(time, type) {
    if (!time) {
        return null
    }
    const format = type || 'date'
    let date = new Date(time)
    let loaclDate = parseTime(date.toLocaleDateString('default'),'{y}-{m}-{d}')
    let localTime = date.toLocaleTimeString('default',{hour12:false}).split(':')[0] === '24' ? replaceStr(date.toLocaleTimeString('default',{hour12:false}),0,'00',':') : date.toLocaleTimeString('default',{hour12:false})
    let time_str
    switch (format) {
        case 'date' : time_str = loaclDate;break;
        case 'time' : time_str = localTime;break;
        case 'dateTime' : time_str = loaclDate + ' ' + localTime;break;
        case 'folderDateTime' : time_str = loaclDate + 'T' + localTime;break;
    }

    return time_str
}
// 表单重置
export function resetForm(refName) {
  if (this.$refs[refName]) {
    this.$refs[refName].resetFields();
  }
}

// 添加日期范围
export function addDateRange(params, dateRange, propName) {
  let search = params;
  search.params = typeof (search.params) === 'object' && search.params !== null && !Array.isArray(search.params) ? search.params : {};
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (typeof (propName) === 'undefined') {
    search.params['beginTime'] = dateRange[0];
    search.params['endTime'] = dateRange[1];
  } else {
    search.params['begin' + propName] = dateRange[0];
    search.params['end' + propName] = dateRange[1];
  }
  return search;
}

export const firstLetter2UpperCase = (str) => str.charAt(0).toUpperCase() + str.slice(1);
/**
 *
 * @param {*} dateRange ["2024-10-21 00:00:00", "2024-09-10 00:00:00"]
 * @param {*} propName 'apply'
 * @param {*} isJoin true
 * @returns { applyStartTime: "2024-10-21 00:00:00", applyEndTime: "2024-10-21 00:00:00", apply: undefined, }
 */
export function dateRangeToObject({ dateRange, requestProp, isJoin = false }) {
  let startProp = 'startTime'
  let endProp = 'endTime'
  let obj = {}
  dateRange = Array.isArray(dateRange) ? dateRange : [];
  if (!isJoin) {
    obj[startProp] = dateRange[0];
    obj[endProp] = dateRange[1];
  } else {
    obj[requestProp + firstLetter2UpperCase(startProp)] = dateRange[0];
    obj[requestProp + firstLetter2UpperCase(endProp)] = dateRange[1];
  }
  obj[requestProp] = undefined
  return obj;
}

// 字符串格式化(%s )
export function sprintf(str) {
  var args = arguments, flag = true, i = 1;
  str = str.replace(/%s/g, function () {
    var arg = args[i++];
    if (typeof arg === 'undefined') {
      flag = false;
      return '';
    }
    return arg;
  });
  return flag ? str : '';
}

// 转换字符串，undefined,null等转化为""
export function parseStrEmpty(str) {
  if (!str || str == "undefined" || str == "null") {
    return "";
  }
  return str;
}

// 数据合并
export function mergeRecursive(source, target) {
  for (var p in target) {
    try {
      if (target[p].constructor == Object) {
        source[p] = mergeRecursive(source[p], target[p]);
      } else {
        source[p] = target[p];
      }
    } catch (e) {
      source[p] = target[p];
    }
  }
  return source;
};

/**
* 参数处理
* @param {*} params  参数
*/
export function tansParams(params) {
  let result = ''
  for (const propName of Object.keys(params)) {
    const value = params[propName];
    var part = encodeURIComponent(propName) + "=";
    if (value !== null && value !== "" && typeof (value) !== "undefined") {
      if (typeof value === 'object') {
        for (const key of Object.keys(value)) {
          if (value[key] !== null && value[key] !== "" && typeof (value[key]) !== 'undefined') {
            let params = propName + '[' + key + ']';
            var subPart = encodeURIComponent(params) + "=";
            result += subPart + encodeURIComponent(value[key]) + "&";
          }
        }
      } else {
        result += part + encodeURIComponent(value) + "&";
      }
    }
  }
  return result
}


// 返回项目路径
export function getNormalPath(p) {
  if (p.length === 0 || !p || p == 'undefined') {
    return p
  };
  let res = p.replace('//', '/')
  if (res[res.length - 1] === '/') {
    return res.slice(0, res.length - 1)
  }
  return res;
}

// 验证是否为blob格式
export function blobValidate(data) {
  return data.type !== 'application/json'
}


/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == "") return "";
  var date = new Date(cellValue)
  var year = date.getFullYear()
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xDC00 && code <= 0xDFFF) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null)
  const list = str.split(',')
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true
  }
  return expectsLowerCase
    ? val => map[val.toLowerCase()]
    : val => map[val]
}

export const exportDefault = 'export default '

export const beautifierConf = {
  html: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'separate',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  },
  js: {
    indent_size: '2',
    indent_char: ' ',
    max_preserve_newlines: '-1',
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: 'normal',
    brace_style: 'end-expand',
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: '110',
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true
  }
}

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, L => L.toUpperCase())
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, str1 => str1.substr(-1).toUpperCase())
}

export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str)
}

/* 手机号加密 */
export function  encryptPhone(mobile) {
  if (!mobile) {
    return '-'
  }
  if (typeof mobile !== 'string') {
    mobile = mobile + ''
  }
  else if (mobile.length >= 11) {
    return mobile.replace(/(.{3})\d*(\d{4})/, "$1****$2")
  }
  // 非11位手机仅展示后4位
  else if (mobile.length > 4 && mobile.length < 11) {
    return mobile.replace(/\d*(\d{4})/, "****$1")
  }
  // 小于4位不做处理
  return mobile
}

/* 姓名加密 */
export function encryptName(name) {
    if (!name) {
        return "-";
    }
    //姓名仅展示第一位
    if (name.length > 1) {
        let str = ''
        for(let i=0;i<name.length;i++){
            str = str + '*'
        }
        return name.substring(0,1)+ str
    }
    return name;
}

/**
 * 导出文件
 * @param res
 * @param filename
 */
export function exportFile(res, filename) {
  const blob = new Blob([res.data], {type: 'application/x-xls'})
  const nav = window.navigator;
  if (nav.msSaveOrOpenBlob) {
    nav.msSaveOrOpenBlob(blob, filename);
  } else {
    const elink = document.createElement('a');
    elink.download = filename;
    elink.style.display = 'none';
    elink.href = URL.createObjectURL(blob);
    document.body.appendChild(elink);
    elink.click();
    URL.revokeObjectURL(elink.href);
    document.body.removeChild(elink);
  }
}

//下载文件下载
export function downloadFile(url) {
  // url = url + "&token=" + localStorage.getItem('token');
  let a = document.createElement("a");
  a.setAttribute("href", encodeURI(url));
  a.setAttribute("target", "_blank");
  a.setAttribute("id", "openwin");
  document.body.appendChild(a);
  a.click();
}

// 数字千位分隔符
export function formatNumber(number) {
  // 数字转为字符串，并按照 .分割
  let arr = (number + '').split('.');
  let int = arr[0] + '';
  let fraction = arr[1] ? arr[1].length > 1 ? arr[1] : arr[1] + '0' : '';
  // 多余的位数
  let f = int.length % 3;
  // 获取多余的位数，f可能是0， 即r可能是空字符串
  let r = int.substring(0, f);
  // 每三位添加','金额对应的字符
  for (let i = 0; i < Math.floor(int.length / 3); i++) {
    r += ',' + int.substring(f + i * 3, f + (i + 1) * 3);
  }
  // 多余的位数，上面
  if (f === 0) {
    r = r.substring(1);
  }
  // 调整部分和小数部分拼接
  return r + (!!fraction ? '.' + fraction : '');
}

// 获取当前日期
export function formatIndexDate(type, currDate) {
    let date = new Date(currDate)
    let year = date.getFullYear()
    let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
    let day = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
    let hours = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
    let minutes = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
    let seconds = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
    if (type === "YYYY-MM-DD") {
        return year + '-' + month + '-' + day + ' '
    }
    return year + '年' + month + '月' + day + '日' + ' ' + hours + ':' + minutes + ':' + seconds
}

// 获取昨天
export function getYesterday() {
    // 获取当前日期和时间
    let today = new Date();

// 获取昨天的日期和时间
    let yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);

// 格式化日期为字符串（可选）
    let year = yesterday.getFullYear();
    let month = String(yesterday.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1并补零
    let day = String(yesterday.getDate()).padStart(2, '0'); // 补零
    return year + '-' + month + '-' + day + ' '
}


/**
 * 文件名称截取
 */
export function getFileNameByUrl(filePath){
  if(!filePath){
    return ''
  }
  const match = filePath.match(/([^\/]+)\/*$/);
  return match[1]
}

/**
 * 货币转换
 * currencyTypeConvert('CNY') 人民币
 * currencyTypeConvert('CNY', 'symbol') ¥
 */
export function currencyTypeConvert(currencyType, to = 'name') {
  let currencyTypeList = {
    CNY: {
      symbol: '¥',
      name: '人民币',
    },
    USD: {
      symbol: '$',
      name: '美元',
    }
  }
  if (!Object.keys(currencyTypeList).includes(currencyType)) {
    console.warn('currencyTypeConvert: currencyType 参数错误')
    return ''
  }
  if (!['symbol', 'name'].includes(to)) {
    to = 'name'
  }
  return currencyTypeList[currencyType][to]
}

/**
 * 金额处理
 */

export function bigjsToFixed(number, currencyType, digits = 2) {
  if (['', null].includes(number) || !isFinite(number)) {
    console.warn('[bigjsToFixed]: number is invalid ' + number);
    return '0'
  }

  const numberFormat = number => String(number).replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  let value = Number.isInteger(number) ? numberFormat(number) : numberFormat(Big(number).toFixed(digits))
  return currencyType
    ? currencyTypeConvert(currencyType, 'symbol') + '' + value
    : value
}

// 预计交货时间转北京时间
export function timestamp2AsiaShanghai(timestamp) {
  return dayjs(timestamp).tz("Asia/Shanghai").format('YYYY-MM-DD')
}

// 判断浏览器内核
export function getBrowserEngine() {
  let engine = null;
  let userAgent = navigator.userAgent;
  if (/Trident\/([\d.]+)/.test(userAgent)) { // 匹配Trident内核（IE浏览器）
    engine = 'Trident';
  } else if (/Gecko\/([\d.]+)/.test(userAgent)) { // 匹配Gecko内核（Firefox浏览器）
    engine = 'Gecko';
  } else if (/AppleWebKit\/([\d.]+)/.test(userAgent)) { // 匹配Webkit内核（Chrome、Safari浏览器）
    engine = 'Webkit';
  } else if (/Presto\/([\d.]+)/.test(userAgent)) { // 匹配Presto内核（Opera浏览器）
    engine = 'Presto';
  }
  return engine;
}


export function getTimeZone() { // 获取时区
  try {
    const { timeZone } = Intl.DateTimeFormat().resolvedOptions();
    if (timeZone) return timeZone;
  } catch (e) {}

  const offset = new Date().getTimezoneOffset();
  const sign = offset > 0 ? "-" : "+";
  const hours = Math.abs(Math.floor(offset / 60));
  const minutes = Math.abs(offset % 60);
  return `UTC${sign}${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
}

export function parseLanguage(lang=navigator.language) { // 解析语言
  const [language, region] = lang.split(/[-_]/);
  return { language, region };
}
