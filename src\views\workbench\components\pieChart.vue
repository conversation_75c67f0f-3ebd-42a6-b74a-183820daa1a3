<template>
  <div id="pieChart" ref="pieChartRef" style="width: 240px; height: 220px;"></div>
</template>

<script setup>
import * as echarts from 'echarts'

const {proxy} = getCurrentInstance()

const props = defineProps({
  pieData: {
    type: Array, // 数据数组
    default: () => [] // 使用函数返回默认值
  },
  title: {
    type: String,
    default: ''
  },
  tooltipName: {
    type: String,
    default: ''
  }
})

let chartInstance = null;

const handleResize = () => {
  if (chartInstance) {
    nextTick(()=>{
      chartInstance.resize();
    })
  }
}

function initChart() {
  if (proxy.$refs.pieChartRef) {
    chartInstance = echarts.init(proxy.$refs.pieChartRef)
    const option = {
      title: {
        text: props.title , // 使用传入的标题或默认标题
        left: 'center',
        bottom: 22,
        textStyle: {
          fontSize: 12, // 修改标题字体大小
          fontWeight: 400,
          color: '#252829'
        }
      },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'horizontal',
        left: 'center',
        bottom: -4,
        icon: 'circle',
        itemGap: 16,
        itemWidth: 10,  // 图标宽度
        itemHeight: 10, // 图标高度
        textStyle: {
          color: '#52585F',
          fontSize: 12
        }
      },
      series: [
        {
          name: props.tooltipName,
          type: 'pie',
          radius: [20, 40],
          data: props.pieData, // 直接使用 props.pieData
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    chartInstance.setOption(option);
  }
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize); // 添加 handleResize 监听器
})

onBeforeUnmount(() => {
  // console.log('组件即将卸载');
  window.removeEventListener('resize', handleResize); // 移除 handleResize 监听器
  if (chartInstance) {
    chartInstance.dispose(); // 销毁 ECharts 实例
  }
});

// 使用 watch 观察 props.pieData 的变化
watch(() => props.pieData, (newData, oldData) => {
  if (newData !== oldData && chartInstance) {
    // 当数据变化时，更新图表
    initChart()
  }
}, {deep: true}); // 使用 deep 选项来观察数组内部的变化

</script>

<style scoped lang="scss">
</style>
