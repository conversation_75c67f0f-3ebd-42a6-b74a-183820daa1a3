<template>
  <div class="auditRecords">
    <div v-if="activities && activities.length">
      <div v-if="title">
      <!--        只有审核状态通过才展示-->
      <div class="mt20 approvalInfor" v-if="authStatus === 'PASS_OA'">{{title}}{{$t('channel.label.approvalTip')}}</div>
      <!--        审核驳回的时候显示FAIL_OA-->
      <div class="mt20 refusedInfor" v-if="authStatus === 'FAIL_OA'">{{title}}{{$t('channel.label.refusedTip')}}</div>
      </div>
      <div v-else>
         <!--        只有审核状态未通过才展示-->
      <div class="mt20 approvalInfor" v-if="authStatus === 'PASS_OA'">{{$t('channel.label.approvalInfor')}}</div>
      <!--        审核驳回的时候显示FAIL_OA-->
      <div class="mt20 refusedInfor" v-if="authStatus === 'FAIL_OA'">{{$t('channel.label.refusedInfor')}}</div>
      </div>
     

      <el-timeline style="width:100%" class="mt20">
        <el-timeline-item
            v-for="(activity, index) in activities"
            :key="index"
            :type="activity.type"
            :color="'#762ADB'"
            :hollow="true"
            :timestamp="parseDateTime(activity.time,'dateTime')"
            placement="top"
        >
          <template #default>
            <el-card class="recordCell">
              <div class="cell" v-if="index == activities.length - 1"><span class="label">{{ $t('channel.label.submitter') }}：</span><span class="value">{{ activity.processor }}({{ activity.processorNo }})</span>
              </div>
              <div class="cell" v-else><span class="label">{{ $t('channel.label.reviewer') }}：</span>
                <span class="value" v-if="index == 0 && activities[0].state == 'success'">{{ $t('channel.label.system') }}</span>
                <span class="value" v-else>{{ activity.processor }}({{ activity.processorNo }})</span>
              </div>
              <div class="cell" v-if="index != activities.length - 1"><span class="label">{{ $t('channel.label.approvalOpinion') }}：</span>
                <span class="value" v-if="index == 0 && activities[0].state == 'success'">{{ $t('channel.label.endProcess') }}</span>
                <span class="value">{{ activity.message }}</span>
              </div>
            </el-card>
          </template>
        </el-timeline-item>
      </el-timeline>
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  activities: {
    type: Array,
    default: () => []
  },
  authStatus: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
})
</script>
<style scoped lang="scss">
.auditRecords{
  width: 100%;
  padding: 0 32px;
  box-sizing: border-box;
  //margin-top: 20px;
  .approvalInfor{
    font-size: 16px;
    color: #52C41A;
    // padding-bottom: 24px;
  }
  .refusedInfor{
    font-size: 16px;
    color: #762ADB;
    // padding-bottom: 24px;
  }
  .recordCell {
    .cell {
      margin-top: 16px;

      .label {
        color: #52585F;
      }

      .value {
        color: #909399;
      }
    }
  }

  :deep(.el-timeline-item__timestamp) {
    font-size: 16px;
    color: #151719;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
  }

  :deep(.el-card) {
    width: 100%;
    background: linear-gradient(180deg, #FAFAFA 0%, #FFFFFF 100%);
    border: 1px solid rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
  }
  .fileName_style{
    align-items: center;
  }
  .mt20{
    margin-top: 20px;
  }
}
</style>
