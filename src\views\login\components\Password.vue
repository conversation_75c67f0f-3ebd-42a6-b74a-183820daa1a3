<template>
  <el-form ref="loginForm" :model="data.loginForm" :rules="data.loginRules" class="login-form" auto-complete="on" label-position="left">
    <el-form-item prop="userName">
      <el-input ref="userName" v-model="data.loginForm.userName" :placeholder="$t('login.username')" name="userName" type="text" tabindex="1"
        auto-complete="on" maxlength="20" @keyup.enter.native="openVerify" />
    </el-form-item>
    <el-form-item prop="password">
      <el-input ref="password" v-model="data.loginForm.password" type="password" show-password :placeholder="$t('login.password')"
        name="password" tabindex="2" auto-complete="on" @keyup.enter.native="openVerify" />
    </el-form-item>

    <el-button class="submit-button" :loading="data.loading" type="primary" @click.native.prevent="openVerify">  {{ $t('login.login') }}</el-button>

    <div style="padding-top: 12px; text-align: right;">
      <span @click="goResetPwd" class="forgot">{{ $t('login.forgotPassword') }}</span>
    </div>
  </el-form>
</template>

<script setup>
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const router = useRouter()
const currentRoute = useRoute()
const {proxy} = getCurrentInstance()

const emit = defineEmits(['submit'])

const loginForm = ref(null)
const password = ref(null)

let showVerify = inject('showVerify')
let loginSuccess = inject('loginSuccess')

const data = reactive({
	loginForm: {
		userName: '',
		password: ''
	},
	loginRules: {
		userName: [
			{ required: true, trigger: 'blur', message: proxy.$t('login.message.username.required') },
		],
		password: [{ required: true, trigger: 'blur', message: proxy.$t('login.message.password.required') }]
	},
	loading: false,
	redirect: undefined
})

watch(currentRoute, (newRoute) => {
  data.redirect = newRoute.query && newRoute.query.redirect;
}, { immediate: true });


function onSuccessVerify(params) {
	console.log(params);
	let captchaVO = {
		...params,
	}
	loginForm.value.validate(valid => {
		if (!valid) return
		data.loading = true
		emit('submit', {
			...data.loginForm,
			captchaVO,
		})
    userStore.login({
      loginType: 'username',
	    systemType:"platform",
      ...data.loginForm,
      captchaVO,
    }).then(() => {
      loginSuccess()
    }).catch(() => {
      data.loading = false;
    });
	})
}

function openVerify() {
	loginForm.value.validate(valid => {
		if (!valid) return;
		showVerify()
	})
}

function resetData() {
  loginForm.value.clearValidate();
  data.loginForm.userName = "";
  data.loginForm.password = "";
}

function goResetPwd() {
	ElMessage({
    message: proxy.$t('login.contactAdmin'),
    type: 'warning',
  })
}

defineExpose({
  onSuccessVerify,
  resetData
})
</script>

<style lang="scss" scoped>
@import "../common.scss";



</style>
