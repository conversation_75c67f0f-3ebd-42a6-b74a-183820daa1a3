<template>
  <section class="queryParamsForm">
    <AddTenant
      v-if="currentEditType === 'add' || currentEditType === 'edit'"
      :tenant-id="currentTenantId"
      :edit-type="currentEditType"
      :systemTypesList="systemTypesList"
      ref="formRef"
    />
    <UpdateContact
      v-else-if="currentEditType === 'contact'"
      :tenant-id="currentTenantId"
      ref="formRef"
    />
    <DetailTenant
      v-else
      ref="formRef"
      :tenant-id="currentTenantId"
      :systemTypesList="systemTypesList"
    />
    <div class="dialog-footer">
        <el-button @click="close">{{ $t('common.cancel') }}</el-button>
        <el-button
          v-if="currentEditType !== 'detail'"
          type="primary"
          :loading="submitLoading"
          @click="submitForm"
        >
          {{ $t('common.confirm') }}
        </el-button>
      </div>
  </section>

</template>

<script setup>
import AddTenant from './components/AddTenant.vue'
import UpdateContact from './components/UpdateContact.vue'
import DetailTenant from './components/DetailTenant.vue'
import { queryTenantSystemTypeList } from "@/api/tenants.js";
const {proxy} = getCurrentInstance()
const route = useRoute()
const router = useRouter()
const formRef = ref(null)
const systemTypesList = ref([])

// 根据props获取编辑类型和ID，不再依赖路由参数
const currentEditType = computed(() => route.query.editType || 'add')
const currentTenantId = computed(() => route.query.id)

// 提交状态
const submitLoading = computed(() => {
  if (!formRef.value) return false
  return formRef.value.submitLoading || false
})

function close() {
  router.push({ path: "/tenants/management" })
  proxy.$tab.closePage(route)
}
// 处理表单提交
async function submitForm() {
  if (currentEditType.value === 'detail') return

  const form = formRef.value
  if (!form) return

  try {
    // 进行表单校验
    await form.accountRef?.validate()
    const result = await form.handleSubmit()
    if (result) {
      // 先关闭当前编辑页
      proxy.$tab.closePage(route)
      // 触发全局事件，通知列表页刷新
      if(currentEditType.value === 'add' || currentEditType.value === 'edit'){
        proxy.$mitt.emit('refreshTenantList')
      }
      // 最后跳转到列表页
      router.push({ path: "/tenants/management" })
    }
  } catch (error) {
    console.error('表单校验失败:', error)
    return false
  }
}
function getTenantSystemTypeList() {
  queryTenantSystemTypeList()
        .then((res) => {
          systemTypesList.value = res.data
        })
        .finally(() => {});
}

onMounted(() => {
  if(currentEditType !== 'contact'){
    getTenantSystemTypeList()
  }
})
</script>
<style lang="scss" scoped>
.dialog-footer {
  padding: 20px 0;
  text-align: right;
}
</style>
