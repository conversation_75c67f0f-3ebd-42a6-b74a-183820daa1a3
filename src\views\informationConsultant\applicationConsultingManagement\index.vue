<template>
  <div class="page applicationConsultingManagement">
    <div class="queryParamsForm">
      <el-form :inline="true" v-model="searchForm" ref="userForm">
        <el-form-item prop="appCode" :label="$t('informationConsultant.label.application')">
          <el-select v-model="searchForm.appCode" clearable :placeholder="$t('common.placeholder.selectTips')">
            <el-option v-for="item in applicationList" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="timeRange" :label="$t('informationConsultant.label.timeRange')">
          <el-date-picker
            v-model="searchForm.timeRange"
            type="daterange"
            :range-separator="$t('informationConsultant.label.to')"
            :start-placeholder="$t('informationConsultant.label.startDate')"
            :end-placeholder="$t('informationConsultant.label.endDate')">
          </el-date-picker>
        </el-form-item>
        <el-form-item class="btn-form">
          <el-button type="primary" @click="searchEvent" v-hasPermi="['sys:applicationConsult:page']">{{ $t("common.search") }}</el-button>
          <el-button @click="resetEvent">{{ $t("common.reset") }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-info">
      <div class="mb10">
        <el-button @click="exportSequenceApplication" type="primary" v-hasPermi="['sys:applicationConsult:exportSequence']">{{ $t("informationConsultant.button.exportSequence") }}</el-button>
        <el-button style="float: right" @click="exportApplication" type="primary" v-hasPermi="['sys:applicationConsult:export']">{{ $t("informationConsultant.button.export") }}</el-button>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%" :key="new Date()" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="enterpriseName" :label="$t('informationConsultant.label.enterpriseName')"></el-table-column>
        <el-table-column prop="appName" :label="$t('informationConsultant.label.applicationName')"></el-table-column>
        <el-table-column prop="" :label="$t('informationConsultant.label.contactInformation')">
          <template #default="{ row }">
            <section>
              <div>
                 <data-marking 
                  :value="row.contact" 
                  type="name"
                  :decrypt-api="() => decryptApi(row, 'contact')"
                />
              </div>
              <div>
                 <data-marking 
                  :prefix="row.contactAreaCode+'-'"
                  :value="row.contactNumber" 
                  type="phone"
                  :decrypt-api="() => decryptApi(row, 'contactNumber')"
                />
              </div>
              <div>
                <data-marking 
                  :value="row.email" 
                  type="email"
                  :decrypt-api="() => decryptApi(row, 'email')"
                />
              </div>
            </section>
          </template>
        </el-table-column>
        <el-table-column prop="intentionDescription" :label="$t('informationConsultant.label.intentionDescription')"></el-table-column>
        <el-table-column prop="createTime" :label="$t('informationConsultant.label.time')">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="tableTotal > 0"
        :total="tableTotal"
        v-model:page="searchForm.page"
        v-model:limit="searchForm.limit"
        @pagination="getTableList"
      />
    </div>
    <ExportSequence
        ref="exportSequenceRef"
        v-model:dialog-visible="dialogVisible"
        :path="`consult:message:export`">
    </ExportSequence>
  </div>
</template>
<script setup name="applicationConsultingManagement">
import {pageList, exportData, exportSequence, viewPrivacy} from "@/api/informationConsultant"
import ExportSequence from "@/components/ExportSequence/index.vue";
import DataMarking from "@/components/DataMarking/index.vue";
import {parseTime} from '@/utils'
const { t } = useI18n();
const exportSequenceRef = ref();
const dialogVisible = ref(false);
  const { proxy } = getCurrentInstance();
  let data = reactive({
    searchForm: initForm(),
    tableLoading: false,
    tableTotal: 0,
    tableData: [],
    selectedRows: [], // 选中的行数据
    idList: [], // 选中的数据ID列表
  });
  const applicationList = computed(() => [
    {
      label: proxy.$t("informationConsultant.applicationList.erp"),
      value: 1,
    },
    {
      label: proxy.$t("informationConsultant.applicationList.wms"),
      value: 2,
    },
    {
      label: proxy.$t("informationConsultant.applicationList.jy"),
      value: 3,
    },
  ]);
  let { searchForm, tableData, tableTotal, tableLoading, selectedRows, idList } = toRefs(data);
  function initForm() {
    return {
      appCode: "",
      startTime: '',
      timeRange: [],
      endTime: '',
      page: 1,
      limit: 20,
    };
  }

  async function decryptApi(row, type){
    // 尝试不同的可能的 ID 字段名
    const id = row?.id;

    if (!row || !id) {
      return Promise.reject(new Error('缺少必要的ID参数'))
    }

    const res = await  viewPrivacy(id)
    row = Object.assign(row, res?.data);
    return res?.data[type] || ''
  }

  // 处理表格选择变化
  function handleSelectionChange(selection) {
    selectedRows.value = selection;
    idList.value = selection.map(row => row.id).filter(id => id);
    console.log('选中的ID列表:', idList.value);
  }
  /** 导出序列*/
  function exportSequenceApplication(){
    exportSequenceRef.value.exportSequenceListPage()
    dialogVisible.value = true;
  }
  function getTableList() {
    tableLoading.value = true;
    searchForm.value.startTime = new Date(searchForm.value.timeRange[0]).getTime();
    searchForm.value.endTime = new Date(searchForm.value.timeRange[1]).getTime();
    pageList(searchForm.value)
      .then((res) => {
        if (res.code !== 0) {
        } else {
          tableData.value = res.data.records.map((item, index) => {
            item.mobilePhoneShow = true;
            return item
          });
          tableTotal.value = parseInt(res.data.total);
        }
      })
      .finally(() => (tableLoading.value = false));
  }

  searchEvent();

  onMounted(() => {
    proxy.$mitt.on('refreshApplicationConsultList', () => {
      getTableList()
    })
  })

  onUnmounted(() => {
    // 清理事件监听
    proxy.$mitt.off('refreshApplicationConsultList')
  })

  function searchEvent() {
    searchForm.value.page = 1;
    getTableList();
  }

  function resetEvent() {
    searchForm.value = initForm();
    getTableList();
  }

  function exportApplication() {
    /* exportData(searchForm.value).then((res)=>{
      downloadBlob(res, proxy.$t('informationConsultant.button.export'))
    }) */
    ElMessageBox.confirm(proxy.$t('common.exportTips'), proxy.$t('common.export'), {
            confirmButtonText: proxy.$t('common.confirm'),
            cancelButtonText: proxy.$t('common.cancel'),
            type: "warning",
        }).then(() => {
          // 如果有选中数据，只传递ID列表；否则传递搜索表单参数
          const exportParams = idList.value.length > 0
            ? { idList: idList.value }
            : searchForm.value;

          console.log('导出参数:', exportParams);
          exportData(exportParams).then((res)=>{
            exportSequenceApplication()
          })
        })
  }
</script>
<style scoped lang="scss">
  .applicationConsultingManagement{

  }
</style>
