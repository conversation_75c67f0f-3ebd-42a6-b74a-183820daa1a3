import { queryOssImageByFileName } from "./api";
import { getBucket } from "./upload"

const isObject = (data) => data !== null && Object.prototype.toString.call(data) === '[object Object]'
const hasHttp = (string) => /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/.test(string)
let ossDomain = ''
let bucket = ''

export const replaceOssDomainAndParams = (params) => {
  if (isObject(params)) {
    for (const key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        const item = params[key];
        if (typeof item === 'string') {
          if (hasHttp(item)) {
            let parsedUrl = processUrl(item)
            if (ossDomain && parsedUrl.origin === ossDomain) {
              params[key] = parsedUrl.pathname.substring(1)
            }
          }
        } else {
          replaceOssDomainAndParams(item)
        }
      }
    }
  } else if (Array.isArray(params)) {
    if (params.length > 0) {
      let hasArrayString = params.every(i => typeof i === 'string')
      if (hasArrayString) {
        params.forEach((item, index) => {
          if (hasHttp(item)) {
            let parsedUrl = processUrl(item)
            if (parsedUrl.origin === ossDomain) {
              params[index] = parsedUrl.pathname.substring(1)
            }
          }
        })
      } else {
        params.forEach(item => replaceOssDomainAndParams(item))
      }
    }
  }
  return params
}

export function setOssDomin(url) {
  ossDomain = url
}

function processUrl(url) {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl
  } catch (err) {
    console.error(`err:processUrl:${url}`, err);
    return url;
  }
}

// 使用方式：getOssImageUrls(array|Object, { imageProp: ['mainPic', 'pic', 'skuPosterPictureUrl'], /*可选children: 'skuList'*/ }
export async function getOssImageUrls(data, options = {
  imageProp: ['mainPic', 'pic']
}) {
  try {
    let urls = getImageUrls(data, options.imageProp)
    let bucket = await getBucket()
    let parms = Object.values(urls).map(item => {
      return {
        bucket,
        fileName: item
      }
    })
    if (parms.length === 0) {
      return Promise.resolve()
    }
    return getOssImageByFileName(parms, data, options)
  } catch (error) {
    console.error('error:getOssImageUrls', error);
  }
}

function getOssImageByFileName(parms, data, options) {
  return queryOssImageByFileName(parms).then(res => {
    if (res.data.urls.length) {
      try {
        const parsedUrl = new URL(res.data.urls[0].url);
        setOssDomin(parsedUrl.origin)
      } catch (err) {}
    }
    loopObject(data, res.data.urls, options)
  })
}

function loopObject(data, responseUrls, options) {
  try {
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        const item = data[key];
        if(isObject(item)){
          loopObject(item, responseUrls, options)
        }
        else if (Array.isArray(item)) {
          if (item.length === 0) continue
          let hasArrayString = item.every(i => typeof i === 'string')
          if (hasArrayString && options.imageProp.includes(key)) {
            data[key + '_origin'] = item
            item.forEach((fileName, fileIndex) => {
              let result = responseUrls.find(oss => oss.fileName === fileName)
              if (result) {
                item[fileIndex] = result.url
              }
            })
          } else {
            item.map(i => loopObject(i, responseUrls, options))
          }
        }
        else if (item && typeof item === 'string') {
          let result = responseUrls.find(oss => oss.fileName === item)
          if (result) {
            data[key + '_origin'] = item
            data[key] = result.url
          }
        }
      }
    }
  }
  catch (error) {
    console.error(error);
  }
}

function getImageUrls(data, props) {
  let res = {}
  for (const key in data) {
    if (Object.prototype.hasOwnProperty.call(data, key)) {
      const item = data[key];
      if(isObject(item)){
        res = Object.assign({}, res, getImageUrls(item, props))
      }
      else if (Array.isArray(item)) {
        if (item.length === 0) continue
        let hasArrayString = item.every(i => typeof i === 'string')
        if (hasArrayString && props.includes(key)) {
          Object.values({ ...item }).map(fileName => {
            if (!hasHttp(fileName)) {
              res[fileName] = fileName
            }
          })
        } else {
          item.map(i => {
            res = Object.assign({}, res, getImageUrls(i, props))
          })
        }
      }
      else if(item && typeof item === 'string') {
        if (props.includes(key) && !hasHttp(item)) {
          res[item] = item
        }
      }
    }
  }
  return res
}
