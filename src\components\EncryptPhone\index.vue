<template>
    <span class="encryptBox">
        <template v-if="!props.nameType">
                <slot name="pre" />{{ mobilePhoneShow ? phone : $encryptPhone(phone) }}
                <el-icon v-if="props.phone" @click="mobilePhoneShow = !mobilePhoneShow" class="encryptBox-icon" color="#762ADB"
                         size="16">
                  <component v-if="!mobilePhoneShow" :is="mobilePhoneShow ? 'Hide' : 'View'"></component>
                </el-icon>
        </template>
        <template v-else>
                <slot name="pre" />{{ nameShow ? name : $encryptName(name) }}
                <el-icon v-if="props.name" @click="nameShow = !nameShow" class="encryptBox-icon" color="#762ADB"
                         size="16">
                  <component  v-if="!nameShow" :is="nameShow ? 'Hide' : 'View'"></component>
                </el-icon>

        </template>
    </span>
</template>
<script setup>
let props = defineProps({
  phone: {
    type: [String, Number],
  },
  name: {
      type: [String, Number],
  },
  nameType: {
      type: Boolean,
      default:false
  }
})

let mobilePhoneShow = ref(false)
let nameShow = ref(false)
</script>
<style lang="scss" scoped>
.encryptBox {
  // display: inline-flex;
  // justify-content: space-between;
  // align-items: center;
  word-wrap: break-word;
  word-break: break-all;
}

.encryptBox-icon {
  margin-left: 4px;
  cursor: pointer;
  // align-self: flex-start;
  vertical-align: text-top;
}
</style>
