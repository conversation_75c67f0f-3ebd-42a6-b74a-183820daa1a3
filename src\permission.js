/*
 * @Author: ch<PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-02 10:23:51
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-01-09 15:45:29
 * @FilePath: \supply-operation-web\src\permission.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import router from './router'
import { ElMessage } from 'element-plus'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { getToken } from '@/utils/auth'
import { isHttp } from '@/utils/validate'
// import { isRelogin } from '@/utils/request'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

NProgress.configure({ showSpinner: false });

const whiteList = ['/login', '/resetPasswordBySMS'];

router.beforeEach(async (to, from, next) => {
  NProgress.start()
  if (getToken()) {
    to.meta.title && useSettingsStore().setTitle(to.meta.title)
    /* has token*/
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else if (whiteList.indexOf(to.path) !== -1) {
      next()
    } else {
      let userStore = useUserStore()
      const loginWhiteList = ['/', '/401', '/404'] // 登录后不校验权限的名单 '/'处理所有标签页关闭时 会跳转至404问题
      if (userStore.userInfo) {
        // const pathList = userStore.sidebarMenu.pathList
        // 处理tagsView刷新
        if (to.path.indexOf('/redirect') !== -1) {
          return next()
        }
        // else if ([...loginWhiteList, ...pathList].indexOf(to.path) === -1) {
        //   return next('/404')
        // }
        next()
      } else {
        try {
          await userStore.getInfo()
          let data = await userStore.getSidebarMenu()
          // Find workbench menu or get first available route
          const workbenchMenu = data.find(menu => menu.menuName === '工作台')
          if (workbenchMenu) {
            next(workbenchMenu.path)
          } else if (data.length > 0) {
            // If no workbench, redirect to first available route
            const firstMenu = data[0]
            if (firstMenu.children && firstMenu.children.length > 0) {
              next(firstMenu.children[0].path)
            } else {
              next(firstMenu.path)
            }
          } else {
            next('/404')
          }
        } catch (error) {
          console.log('error', error);
          await userStore.logOut()
          ElMessage.error(error)
          // next({ path: '/' })
          next(`/login?redirect=${to.path}`)
          NProgress.done()
        }
      }
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

router.afterEach(() => {
  NProgress.done()
})
