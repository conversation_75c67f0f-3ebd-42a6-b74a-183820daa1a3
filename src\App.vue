<template>
  <section style="height: 100%">
      <!-- 开启水印 -->
      <el-watermark
            v-if="watermarkEnabled"
            :font="{ color: fontColor }"
            :content="userStore.userInfo?.nickName"
            :z-index="9999"
            style="height: 100vh;width: 100%"
    >
        <router-view />
    </el-watermark>
    <!-- 关闭水印 -->
     <router-view v-else />
  </section>
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings'
import { handleThemeStyle } from '@/utils/theme'
import { getBrowserEngine } from '@/utils/index'
const route = useRoute();
import useUserStore from '@/store/modules/user'
const userStore = useUserStore()

// 兼容火狐
let descriptionsLayout = getBrowserEngine() === 'Gecko' ? 'auto' : 'fixed'
const watermarkEnabled = computed(() => {
    return useSettingsStore().watermarkEnabled && route.path !== "/login"
})
// 明亮/暗黑主题水印字体颜色适配
const fontColor = computed(() => {
    return "rgba(0, 0, 0, .15)"
});

onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme)
  })
})
</script>

<style lang="scss">
:root {
  --text-color: #252829;     // 基本色
  --text-color-grey:#90979F; // 辅助灰
  --color-primary: #762ADB;
}

.page {
  &-block {
    background: #fff;
    padding: 16px;
    margin-bottom: 10px;
  }
}


.moduleTitle {
  font-weight: 600;
  font-size: 16px;
  color: #252829;
  line-height: 16px;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  &::before {
    content: " ";
    display: inline-block;
    width: 3px;
    height: 14px;
    background: var(--color-primary);
    border-radius: 2px;
    margin-right: 6px;
  }
}
.ui-descriptions {
  --el-border-color-lighter: #DCDCDC;
  .el-descriptions__table {
    table-layout: fixed;
    --el-descriptions-item-bordered-label-background: rgba(240, 241, 245, .85);
    .el-descriptions__label {
      width: 108px;
      background: rgba(240, 241, 245, .85);
      font-weight: 400;
      color: #909090;
      line-height: 20px;
      padding: 6px 11px;
    }
    .el-descriptions__content {
      font-weight: 400;
      color: #252829;
      padding: 6px 11px;
      word-wrap: break-word;
      word-break: break-all;
    }
  }
}

.el-dialog__footer {
  padding-top: 12px !important;
  padding-right: 16px;
  box-shadow: inset 0px 1px 0px 0px #ECEEF1;
}

.el-dialog {
  padding: 16px 0 12px !important;
}

.el-dialog__header {
  padding-left: 16px;
  box-shadow: inset 0px -1px 0px 0px #ECEEF1;
  line-height: 24px;
}

.el-dialog__body {
  padding: 16px;
}


.ui-normalTable.el-table--border {
  // background: #ECEEF1;
  --el-table-border-color: #DCDCDC;
  &::after {
    display: none;
  }
  thead {
    --el-table-header-text-color: var(--text-color);
  }
  .el-table__header-wrapper, .el-table__fixed-header-wrapper {
		th {
			background-color: #ECEEF1 !important;
      color: #151719;
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
		}
	}
  .el-table__inner-wrapper {
    &::before {
      display: none;
    }
  }

  .el-table__empty-block {
    border-right: 1px solid var(--el-table-border-color);
    border-bottom: 1px solid var(--el-table-border-color);
    box-sizing: border-box;
  }
}

.el-space--vertical {
  max-width: 100%;
}
.el-space__item > * {
  min-width: 0;
}

.doublePage {
  height: 100%;
  display: flex;
  flex-direction: column;
  .doubleArea {
    flex: 1;
    display: flex;
    height: 0;
    overflow-y: auto;
    flex-direction: column;
    margin-bottom: 10px;
    &:first-child {
      flex: 1.5;
    }
    &:last-child {
      margin-bottom: 0;
    }
    &-tbHeader {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      width: 100%;
    }
    &-title {
      font-weight: 600;
      font-size: 14px;
      color: #252829;
      line-height: 16px;
      display: flex;
      align-items: center;
      &::before {
        content: " ";
        display: inline-block;
        width: 3px;
        height: 14px;
        background: var(--color-primary);
        border-radius: 2px;
        margin-right: 6px;
      }
    }
    &-bd {
      flex: 1;
      height: 0;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      background: #fff;
      padding: 16px;
    }
    &-ft {
      flex-shrink: 0;
      .pagination-container {
        margin-top: 0;
      }
    }
    &-table {
      flex: 1;
    }
    .queryParamsForm {
      padding-top: 12px;
      padding-bottom: 0;
      .el-form--inline .el-form-item {
        margin-bottom: 12px;
      }
    }

  }
}
</style>
