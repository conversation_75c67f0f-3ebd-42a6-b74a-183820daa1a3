<template>
  <el-dialog :value="isVisible" :title="title" :close-on-click-modal="false" width="30%" @close="cancelBtn">
    <div class="menu-box">
      <div class="menu-tag" v-for="(item, index) in newMenuList" :key="item.menuId">
        <div @click="handleMenuLink(item)">{{ item.menuName }}</div>
        <el-icon class="el-icon-close" color="#FF4D4F" @click="delMenuList(item)">
          <CircleCloseFilled/>
        </el-icon>
      </div>
    </div>
    <div style="height: 200px;">
      <el-cascader ref="saveMenuRef"
                   v-model="menuId"
                   @change="onChooseMenu"
                   :props="{ label: 'menuName', value: 'menuId', children: 'childAuthorityMenuList' }"
                   :placeholder="$t('workbench.placeholder.inputTips')"
                   :options="menuList">
      </el-cascader>
    </div>
    <template #footer>
      <div>
        <el-button class="oper-btn" @click="cancelBtn">{{$t('workbench.btn.cancelBtn')}}</el-button>
        <el-button class="oper-btn" type="primary" @click="addBtn">{{$t('workbench.btn.saveBtn')}}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {ElMessage} from "element-plus";
import {addHomeMenu} from "@/api/workbench.js"

const {t} = useI18n();
const {proxy} = getCurrentInstance()
const emit = defineEmits()

defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  menuList: {
    type: Array,
    default: false
  },
  homeMenuList: {
    type: Array,
    default: false
  }

})

const menuId = ref('')
const saveMenuRef = ref()
const data = reactive({
  newMenuList: [],
  selectedMenuList: []
})

const {newMenuList, selectedMenuList} = toRefs(data)

function setMenuList(data) {
  newMenuList.value = data
  let menuIds = []
  newMenuList.value.forEach(item => {
    menuIds.push(item.menuId)
  })

  selectedMenuList.value = menuIds
}

function handleMenuLink() {

}

function onChooseMenu(value) {
  let menuObj = {}
  if (!value.length) {
    menuObj.menuId = undefined
    menuObj.menuName = undefined
  } else {
    menuObj.menuId = value[1]
    let pathLabels = saveMenuRef.value.getCheckedNodes()[0].pathLabels
    menuObj.menuName = pathLabels[1]
    menuObj.path = saveMenuRef.value.getCheckedNodes()[0].data.path
    if (!selectedMenuList.value.includes(menuObj.menuId)) {
      newMenuList.value.push(menuObj)
      selectedMenuList.value.push(menuObj.menuId)
    } else {
      ElMessage.warning(t('workbench.message.menuExistTips'))
    }
  }
}

function delMenuList(item) {
  selectedMenuList.value.splice(selectedMenuList.value.indexOf(item.menuId), 1);
  newMenuList.value.splice(newMenuList.value.indexOf(item), 1);
}

function cancelBtn() {
  isVisible.value = false
  reset()
}

function reset() {
  menuId.value = ''
}

function addBtn() {
  let params = {
    menuIds: selectedMenuList.value.toString()
  }
  addHomeMenu(params).then(res => {
    if (res.code === 0) {
      ElMessage.success(t('workbench.message.saveSuccess'))
      menuId.value = ''
      emit('submitted')
      isVisible.value = false
    }
  })
}

let dialogVisible = ref(false)
let isVisible = computed({
  get() {
    return dialogVisible.value
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

defineExpose({
  setMenuList
})
</script>

<style scoped lang="scss">
.menu-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 16px;

  .menu-tag {
    margin-right: 12px;
    background: #FBFBFB;
    border-radius: 4px;
    border: 1px solid #EDEFF2;
    text-align: center;
    padding: 8px 14px;
    font-weight: 500;
    font-size: 14px;
    color: #323233;
    line-height: 20px;
    position: relative;

    .el-icon-close {
      position: absolute;
      width: 22px;
      height: 22px;
      top: -8px;
      right: -8px;
    }
  }
}

.oper-btn {
  line-height: 14px;
  width: 60px;
  height: 32px;
  border-radius: 2px;
}

</style>
