import request from '@/utils/request'

// 原版OSS
export function getOssStsToken(params) {
  return request({
    url: '/supply-base/aliyunOss/sts/token',
    method: 'get',
    params: params
  })
}

// 新版OSS接口
export function getYtoOssStsToken(data) {
  return request.get('/supply-base/aliyunOss/sts/yto/token', data);
}

// 通过文件名批量获取图片真实路径
export function queryOssImageByFileName(data) {
  return request({
    url: '/aliyunOss/sts/yto/previewFile',
    method: 'post',
    data
  })
}
