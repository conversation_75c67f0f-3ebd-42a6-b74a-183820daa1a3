export default {
  // 路由国际化
  route: {
    workbench: "Рабочий стол",
    afterSales: "После продажи",
    afterSalesReasons: "Причины после продажи",
    afterSalesApplication: "Заявка после продажи",
  },
  afterSales: {
    addReasonsTitle: 'Добавить причины после продажи',
    editReasonsTitle: 'Изменить причины после продажи',
  },
  common: {
    screenfull: 'Полный экран / окно',
    add: 'Добавить',
    edit: 'Редактирование',
    delete: 'Удалить',
    batchDelete: 'Удаление пакетов',
  },
  user: {
    profile: 'Индивидуальный центр',
    logout: 'Выход из системы',
  },
  // 登录页面国际化
  login: {
    username: "用户名",
    password: "密码",
    login: "登 录",
    captchaCode: "验证码",
    capsLock: "大写锁定已打开",
    message: {
      username: {
        required: "请输入用户名",
      },
      password: {
        required: "请输入密码",
        min: "密码不能少于6位",
      },
      captchaCode: {
        required: "请输入验证码",
      },
    },
  },

  langSelect: {
    message: {
      success: "Переключиться успешно!",
    },
  },
};
