.loginPage {
  background: #000;
  // background: linear-gradient( 218deg, #FFDA8A 0%, #FFDA8A 9%, #FFB352 100%);
  height: 100%;
}
.login {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  &-bd {
    padding: 0 40px;
  }
}

.login-welcome {
  height: 82px;
  vertical-align: top;
  position: absolute;
  z-index: 100;
  top: 4.375vw;
  left: 4vw;
  font-size: 36px;
  color: #FFFFFF;
  line-height: 43px;
  &__text {
    margin: 0;
    font-family: DingTalk, DingTalk;
    font-size: 32px;
    color: #762ADB;
    line-height: 39px;
    text-align: right;
    font-style: normal;
  }
  &__title {
    font-weight: 400;
    font-family: DingTalk, DingTalk;
    font-size: 21px;
    color: #762ADB;
    line-height: 25px;
    text-align: right;
    font-style: normal;
  }
  &__line{
    width: 23px;
    height: 6px;
    background: #762ADB;
    display: block;
    margin-top: 30px;
  }
}

.login-left {
  width: 30%;
  height: 100%;
  vertical-align: top;
  position: relative;
  z-index: 10;
  object-fit: cover;
  min-width: 0;
}
.link-div{
  a:hover{
    color: #762ADB;
  }
}

@media screen and (max-width: 1540px) { /*当屏幕尺寸小于600px时，应用下面的CSS样式*/

}

.login-container {
  flex: 1;
  position: relative;
  z-index: 10;
  flex-shrink: 0;
  width: 628px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: #FFFFFF;
  flex-shrink: 0;
  &__hd {
    padding: 67px 40px 63px;
  }
  &__logo {
    width: 50px;
    height: 50px;
    vertical-align: top;
    margin-right: 8px;
  }
  &__title {
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    font-size: 32px;
    color: #252829;
    line-height: 28px;
    letter-spacing: 3px;
    text-align: right;
    font-style: normal;
    margin: auto;
  }

  .el-form-item {
    // border: 1px solid rgba(255, 255, 255, 0.1);
    // background: rgba(0, 0, 0, 0.1);
    // border-radius: 5px;
    // color: #454545;
  }
}


:deep(.login-form) {
	position: relative;
	padding: 0 0 0;
	margin: 0 auto;
	overflow: hidden;

  .el-form-item {
    margin-bottom: 20px;
    // border-bottom: 1px solid #EAEAEA;
    // padding: 0 0 17px 0;
  }
  .el-input__wrapper {
    padding: 14px 16px;
    // border: none;
    // box-shadow: none;
    // padding-left: 0;
  }
  .el-form-item.is-error .el-input__wrapper {
    // box-shadow: none;
  }
  .el-form-item__error {
    padding-top: 0;
  }
  .el-form-item__content {
    line-height: initial;
  }
  .el-input__clear {
    line-height: 22px;
  }

  .el-input__inner {
    vertical-align: top;
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #151719;
    height: 22px;
    // border: none;
    padding: 0 !important;
    border-radius: 0;
    &::placeholder {
      color: #CCCFD5;
    }
    &:-webkit-autofill {
      box-shadow: 0 0 0 1000px white inset !important;
    }
  }

  .countDown {
    height: 22px;
    display: inline-block;
    min-width: 80px;
    font-weight: 600;
    font-size: 16px !important;
    line-height: 22px;
    border: 0;
    // border-bottom: 1px solid #EAEAEA;
    // padding-bottom: 17px !important;
    text-align: center;
    border-radius: 0;
    box-sizing: border-box;
    color: var(--color-primary) !important;
    margin-left: 0;
    &.is-wait {
      color: #90979F !important;
      min-width: 100px;
      font-weight: 400;
    }
  }

  .submit-button {
    width: 100% !important;
    height: 48px;
    background: var(--color-primary) !important;
    border-color: var(--color-primary) !important;
    border-radius: 2px;
    font-weight: 500;
    font-size: 16px !important;
    color: #FFFFFF;
  }
}


.forgot {
  cursor: pointer;
  font-family: PingFangSC, PingFang SC;
  font-weight: 400;
  font-size: 14px;
  color: #51585F;
  line-height: 20px;
}
