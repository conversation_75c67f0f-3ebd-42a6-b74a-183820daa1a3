<template>
    <section class="app-main" id="app-main">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-transform" mode="out-in">
            <keep-alive :include="tagsViewStore.cachedViews">
              <component v-if="!route.meta.link" :is="Component" :key="route.path"/>
            </keep-alive>
          </transition>
        </router-view>
        <iframe-toggle />
    </section>
</template>

<script setup>
import iframeToggle from "./IframeToggle/index"
import useTagsViewStore from '@/store/modules/tagsView'

const tagsViewStore = useTagsViewStore()
import useUserStore from '@/store/modules/user'
const userStore = useUserStore()
const font = reactive({
  color: 'rgba(0, 0, 0, .08)',
})
let str = ''
if(userStore.userInfo.nickName){
  str = userStore.userInfo.nickName
}
if(userStore.userInfo.mobile){
  str += '('+ userStore.userInfo.mobile.substr(userStore.userInfo.mobile.length - 4) +')'
}
const content = ref([str])
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  height: 100%;
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  // overflow: hidden;
}

.elWatermark {
  height: 100%;
  min-height: 100%;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 90 = navbar + tags-view = 50 + 40 */
    padding: 10px;
    min-height: calc(100vh - 90px);
  }

  .fixed-header + .app-main {
    padding-top: 90px;
  }
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 6px;
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}
</style>

