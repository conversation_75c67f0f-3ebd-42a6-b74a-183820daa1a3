import request from "@/utils/request";

export function pageList(data = {}) { // 分页查询
  return request({
    url: '/supply-base/baseConsultMessage/page',
    method: 'post',
    data
  })
}
export function exportData(data = {}) { // 导出
  return request({
    url: '/supply-base/baseConsultMessage/export',
    method: 'post',
    data
  })
}
export function exportSequence(data = {}) { // 导出序列
  return request({
    url: '/supply-base/baseConsultMessage/exportSequence',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

export function viewPrivacy(id = null) { // 隐私信息查看
  return request({
    url: `/supply-base/baseConsultMessage/viewPrivacy/${id}`,
    method: 'get',
  })
}
