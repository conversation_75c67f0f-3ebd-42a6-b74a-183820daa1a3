import vue from '@vitejs/plugin-vue'

import createAutoImport from './auto-import'
import createAutoImportComponents from "./vue-components";
import createSvgIcon from './svg-icon'
import createCompression from './compression'
import createSetupExtend from './setup-extend'
import Icons from "unplugin-icons/vite";

export default function createVitePlugins(viteEnv, isBuild = false) {
    const vitePlugins = [vue()]
    vitePlugins.push(createAutoImport())
    vitePlugins.push(createAutoImportComponents())
    vitePlugins.push(createSetupExtend())
    vitePlugins.push(createSvgIcon(isBuild))
    vitePlugins.push(Icons({
        autoInstall: true,
    }))
    isBuild && vitePlugins.push(...createCompression(viteEnv))
    return vitePlugins
}
