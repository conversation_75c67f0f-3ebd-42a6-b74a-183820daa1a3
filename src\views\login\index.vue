<template>
  <div class="loginPage">
    <div class="login-welcome">
      <img src="~@/assets/images/login_title.png" class="login_title">
      <!-- <p class="login-welcome__text">welcome！</p>
      <span class="login-welcome__title">{{ data.title }}</span>
      <span class="login-welcome__line"></span> -->
    </div>
    <div class="login">
      <img src="~@/assets/images/login-left.png" class="login-left">
      <div class="login-container">
        <div style="width: 480px;margin: auto;margin-top: 15%;">
          <el-row class="login-container__hd" type="flex" align="middle">
            <!-- <img xsrc="~@/assets/logo-2.png" class="login-container__logo" alt="logo"> -->
            <span class="login-container__title">{{ data.title }}</span>
          </el-row>
          <div class="login-bd">
            <el-tabs class="modify-tabs" v-model="data.loginType"  @tabChange="tabChangeAction">
              <el-tab-pane label="账号登录" name="username">
                <Password ref="loginByPassword" />
              </el-tab-pane>
              <el-tab-pane label="手机号登录" name="mobile">
                <ShortMessage ref="loginByShortMessage" />
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
      </div>
    </div>

    <div style="position: absolute;bottom: 0;text-align: center;width: 100%;z-index:9999;color: #90979f" class="link-div">
      <div style="line-height: 32px;display: flex;align-items: center;justify-content: center;"><a href="https://beian.miit.gov.cn/" target="_blank">浙ICP备2022012241号-6</a><img style="width: 17px;height: 17px;margin:0px 10px 0px 15px;" src="@/assets/images/beianImg.png" /><a href="https://beian.mps.gov.cn/#/query/webSearch?code=33041102000638" rel="noreferrer" target="_blank">浙公网安备33041102000638号</a></div>
    </div>

    <Verify @success="onSuccessVerify"
      ref="verify"
      :vSpace="16"
      :mode="verifyOptions.mode"
      :captchaType="verifyOptions.captchaType"
      :imgSize="verifyOptions.imgSize"
    />
  </div>

</template>

<script setup>
import Password from './components/Password.vue'
import ShortMessage from './components/ShortMessage.vue'

const currentRoute = useRoute()
const router = useRouter()
const { proxy } = getCurrentInstance()

const verify = ref(null)
const loginByPassword = ref(null)
const loginByShortMessage = ref(null)

const data = reactive({
	verifyOptions: {
		captchaType: 'blockPuzzle',
		mode: 'pop',
		imgSize: { width: '400px', height: '180px' },
	},
	title: import.meta.env.VITE_APP_TITLE,
	loginType: 'username', // username 密码登录 mobile 验证码
	redirect: undefined
})

let { verifyOptions } = toRefs(data)
provide('showVerify', showVerify)
provide('loginSuccess', loginSuccess)

watch(currentRoute, (newRoute) => {
  data.redirect = undefined
  // data.redirect = newRoute.query && newRoute.query.redirect;
  if (newRoute.query && newRoute.query.redirect) {
    let [redirectPath, redirectQuery] = newRoute.query.redirect.split('?');
    console.log(redirectPath, redirectQuery);
    data.redirect = {
      redirectPath,
      redirectQuery: redirectQuery ? urlParamToJson(redirectQuery) : {},
    }
    console.log('data.redirect', data.redirect);

  }
}, { immediate: true });

function tabChangeAction() {
  loginByPassword.value.resetData();
  loginByShortMessage.value.resetData();
}
function urlParamToJson(url) {
  if (!url) {
    return {};
  }

  let json = {};
  url.substring(url.indexOf('?') + 1)
    .trim()
    .split('&')
    .forEach(item => json[item.split('=')[0]] = item.split('=')[1]);

  return json;
}



function showVerify() {
	verify.value.show()
}

function onSuccessVerify(params) {
	if (data.loginType === 'username') {
		loginByPassword.value.onSuccessVerify({
			...params,
			...data.verifyOptions,
		})
	} else if (data.loginType === 'mobile') {
		loginByShortMessage.value.onSuccessVerify({
			...params,
			...data.verifyOptions,
		})
	}
}

function loginSuccess() {
	router.push({ path: data.redirect ? data.redirect.redirectPath : '/', query: data.redirect ? data.redirect.redirectQuery : {} })
}
</script>
<style>

</style>
<style lang="scss" scoped>
@import "./common.scss";

/* reset element-ui css */
:deep(.login-container) {
  .el-tabs__header {
    margin-bottom: 30px;
  }
  .el-tabs__item {
    font-family: PingFangSC, PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #90979F;
    padding-bottom: 12px;
    height: auto;
    line-height: 22px;
    &.is-active {
      color: #151719;
    }
    &:focus-visible {
      box-shadow: none;
    }
  }
  .el-tabs__nav-wrap::after {
    display: none;
  }
  .el-tabs__active-bar {
    padding: 0 15px;
    background: none;
    &::after {
      content: " ";
      display: block;
      width: 100%;
      height: 3px;
      background: #FF7D21;
      border-radius: 5px;
      background-color: var(--color-primary);
    }
  }
}

.login_title{
  width: 183px;
  height: 112px;
}

</style>
