<template>
    <div class="page">
        <div class="block">
            <span class="demonstration">Timestamp</span>
            <div class="demonstration">传给接口：{{ value1 }}</div>
            <div class="demonstration">页面显示：{{ parseDateTime(value1,'dateTime') }}</div><!--date:日期，time:时间，dateTime:日期+时间-->
            <el-date-picker
                    v-model="value1"
                    type="datetime"
                    placeholder="Pick a Date"
                    format="YYYY/MM/DD HH:mm:ss"
                    value-format="x"
            />
        </div>
    </div>
</template>
<script setup name="AfterSalesReasons">
    const { proxy } = getCurrentInstance()

    let data = reactive({
        value1:'',
    })
    let reasonsDialog = ref({
    })

    let { value1 } = toRefs(data);
</script>

<style scoped lang="scss"></style>