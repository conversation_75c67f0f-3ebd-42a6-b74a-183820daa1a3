import request from '@/utils/request'

export function addDept(data) { // 新增部门
    return request({
        url: '/supply-base/base/dept/add',
        method: 'post',
        data
    })
}

export function deleteDept(deptId='') { // 删除部门
    return request({
        url: `/supply-base/base/dept/del/${deptId}`,
        method: 'get',
    })
}

export function getDeptDetail(params={}) {  // 获取部门详情
    return request({
        url: '/supply-base/base/dept/detail',
        method: 'get',
       params
    })
}

export function getDepts(data={}) {  // 获取部门列表
    return request({
        url: '/supply-base/base/dept/list',
        method: 'post',
        data
    })
}

export function getDeptTree(params={}) {  // 获取部门树
    return request({
        url: '/supply-base/base/dept/tree',
        method: 'get',
        params
    })
}


export function updateDept(data) { // 编辑部门
    return request({
        url: '/supply-base/base/dept/edit',
        method: 'post',
        data
    })
}

