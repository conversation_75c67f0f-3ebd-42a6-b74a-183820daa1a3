import { createApp } from 'vue'

import Cookies from 'js-cookie'

import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import locale from 'element-plus/es/locale/lang/zh-cn'

import '@/assets/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive

// 注册指令
import plugins from './plugins' // plugins

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // permission control
import i18n from "@/lang";

import { parseTime, resetForm, addDateRange, parseDateTime, formatIndexDate, formatNumber, getAssetsUrl, getFileNameByUrl, bigjsToFixed, timestamp2AsiaShanghai, encryptPhone, encryptName} from '@/utils/index'
import { isImageFile } from '@/utils/validate'
import { dayjs } from 'element-plus'
// 扩展插件
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import mitt from 'mitt'
dayjs.extend(utc)
dayjs.extend(timezone)
const app = createApp(App)
const Mit = mitt()

// 全局方法挂载
app.config.globalProperties.$getAssetsUrl = getAssetsUrl
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.parseDateTime = parseDateTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.formatIndexDate = formatIndexDate
app.config.globalProperties.formatNumber = formatNumber
app.config.globalProperties.$isImageFile = isImageFile
app.config.globalProperties.$getFileNameByUrl = getFileNameByUrl
app.config.globalProperties.$toFixed = bigjsToFixed
app.config.globalProperties.$timestamp2AsiaShanghai = timestamp2AsiaShanghai
app.config.globalProperties.$encryptPhone = encryptPhone
app.config.globalProperties.$encryptName = encryptName

// 添加到全局属性中
app.config.globalProperties.$mitt = Mit

app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.use(i18n);
app.component('svg-icon', SvgIcon)

directive(app)

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale: locale,
  // 支持 large、default、small
  size: Cookies.get('size') || 'default'
})

app.mount('#app')
