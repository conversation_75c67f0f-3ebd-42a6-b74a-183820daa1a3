<template>
  <!-- 上传组件 -->
  <el-upload
      v-model="imgUrl"
      v-loading="loading"
      class="single-uploader"
      :show-file-list="false"
      :before-upload="handleBeforeUpload"
      :http-request="uploadFile"
      :element-loading-text="loadingText"
      :accept="accept"
      :list-type="listType"
      :on-success="handleUploadSuccess"
      :disabled="isDisabled"
  >

    <div v-if="imgUrl" class="preview-content" @click.stop="">
      <img :src="imgUrl" class="preview-content__image" :style="imageSizeCss"/>
      <div class="preview-content__mask">
        <div class="preview-content__actions">
            <span title="预览" class="svg-container" @click.stop="handlePreview">
                <i-ep-ZoomIn/>
            </span>
          <span title="移除" class="svg-container" @click.stop="handleRemove" v-if="!isDisabled">
             <i-ep-Delete/>
            </span>
        </div>
      </div>
      <div class="preview-content__status-label">
                <span class="svg-container">
                     <i-ep-Check/>
                </span>
      </div>
    </div>
    <el-icon v-else class="single-uploader__icon" :style="imageSizeCss">
      <i-ep-plus/>
    </el-icon>

  </el-upload>
  <el-image-viewer  v-if="previewVisible" @close="closeViewer" :url-list="[imgUrl]" />
</template>

<script setup lang="ts">
import {PropType} from "vue";
import {UploadRawFile, UploadRequestOptions} from "element-plus";
import type {EpPropMergeType} from "element-plus/es/utils/vue/props/types"
import { commonUpload } from "@/api/oss";

import { useVModel } from '@vueuse/core';

const props = defineProps({
  modelValue: {
    type: String,
    default: "",
  },
  accept: {  //接受上传的文件类型
    type: String,
    default: "image/*",
  },
  fileSize: {
    type: Number,
    default: 3// 限制大小单位M
  },
  listType: {  //文件列表的类型
    type: String as PropType<EpPropMergeType<StringConstructor, "text" | "picture" | "picture-card", unknown>>,
    default: "picture-card",
  },
  loadingText: {  //loading文本
    type: String,
    default: "",
  },
  imageSize: { //设置图片大小
    type: [Number, String],
    default: '130px'
  },
  height: { //设置图片高
    type: [Number, String],
    default: ''
  },
  width: { //设置图片宽
    type: [Number, String],
    default: ''
  },
  fileType: { //接受上传的文件格式
    type: Array,
    default: () => ["png", "jpg", "jpeg"],
  },
  formRef: {
    type: Object,
    default: () => {
    }
  },
  name: {
    type: String,
    default: ''
  },
  isDisabled:{
     type: Boolean,
     default: false
  }

});

const emit = defineEmits(["update:modelValue", 'input']);
const imgUrl = useVModel(props, "modelValue", emit);


const imageSizeCss = computed(() => {
  let width = (typeof props.width === "string" ? props.width : `${props.width}px`) || (typeof props.imageSize === "string" ? props.imageSize : `${props.imageSize}px`)
  let height = (typeof props.height === "string" ? props.height : `${props.height}px`) || (typeof props.imageSize === "string" ? props.imageSize : `${props.imageSize}px`)
  return Object.assign({}, {width, height})
})
const state = reactive({
  loading: false,
  previewVisible: false,
})
const {
  loading,
  previewVisible,
} = toRefs(state);

/**
 * 自定义图片上传
 *
 * @param options
 */
async function uploadFile(options: UploadRequestOptions): Promise<any> {
  loading.value = true
  await commonUpload(options.file).then(res => {
    imgUrl.value = res.url;
    props.formRef.validateField(props.name)
  }).finally(() => {
    loading.value = false
  })

}

/**
 * 限制用户上传文件的格式和大小
 */
function handleBeforeUpload(file: UploadRawFile) {
  const type = (file.type).replace('image/', '')
  const isLimitImage = props.fileType.includes(type);
  if (!isLimitImage) {
    ElMessage.error(`上传图片只能是 ${props.fileType} 格式!`);
    return false
  }
  const fileSize = file.size / 1024 / 1024 <= props.fileSize;
  if (!fileSize) {
    ElMessage.error(`上传图片不能大于${props.fileSize}M`);
    return false
  }

  return true;
}

function closeViewer(){
  state.previewVisible = false
}


/**
 * 预览图片
 */
function handlePreview() {
  state.previewVisible = true
}

/**
 * 上传成功
 */
function handleUploadSuccess() {
  emit('input', imgUrl.value);
}

/**
 * 移除图片
 */
function handleRemove() {
  imgUrl.value = '';
  emit('input', '');
}

</script>

<style scoped lang="scss">
.single-uploader {
  overflow: hidden;
  cursor: pointer;
  border: 1px var(--el-border-color) solid;
  border-radius: 6px;

  .preview-content {
    position: relative;
    overflow: hidden;

    &__image {
      display: block;
    }

    &__mask {
      opacity: 0;
      position: absolute;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      transition: all 0.3s;

      &:hover {
        opacity: 1;
      }
    }

    &__actions {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      .svg-container {
        color: #fff;
        font-size: 20px;
        margin: 0 6px;
      }
    }

    &__status-label {
      position: absolute;
      right: -15px;
      top: -6px;
      width: 40px;
      height: 24px;
      background: #67C23A;
      text-align: center;
      transform: rotate(45deg);
      line-height: normal;

      .svg-container {
        position: absolute;
        right: 13px;
        top: 10px;
        color: #fff;
        font-size: 12px;
        transform: rotate(-45deg);
      }
    }

  }

  :deep(.el-upload--picture-card) {
    width: unset;
    height: unset;
    line-height: normal;
    border: none;
    text-align: left;
    background-color: unset;
  }
}
</style>
