<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <!-- <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!settingsStore.topNav" /> -->
    <!-- <top-nav id="topmenu-container" class="topmenu-container" v-if="settingsStore.topNav" /> -->
    <div class="navbar-inner"></div>
    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <!-- 语言选择 -->
        <!-- <lang-select class="right-menu-item hover-effect langSelect" /> -->
        <!-- <el-tooltip :content="$t('common.screenfull')" effect="dark" placement="bottom">
          <screenfull id="screenfull" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      </template>

      <el-dropdown @command="handleCommand" class="right-menu-item hover-effect" trigger="hover">
        <div class="avatar-wrapper">
          <img :src="userStore.avatar || $getAssetsUrl('common/default-avatar.png')" class="user-avatar" />
          <span v-if="userStore.userInfo">{{ userStore.userInfo.nickName }}</span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- <router-link to="/user/profile">
              <el-dropdown-item>{{ $t('user.profile') }}</el-dropdown-item>
            </router-link> -->
            <!-- <el-dropdown-item command="setLayout" v-if="settingsStore.showSettings">
              <span>布局设置</span>
            </el-dropdown-item> -->
            <el-dropdown-item command="changePassword">
              <span>修改密码</span>
            </el-dropdown-item>
            <!-- <el-dropdown-item divided command="logout">
              <span>{{ $t('user.logout') }}</span>
            </el-dropdown-item> -->
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <screenfull id="screenfull" style="margin-left: 17px;" class="right-menu-item hover-effect" />
      <span class="right-menu__logout" @click="logout">
        <span style="margin-right: 6px;">退出</span>
        <el-icon :size="16"><SwitchButton /></el-icon>
      </span>

    </div>
    <ChangePassword ref="changePasswordRef" :title="proxy.$t('changePassword.title.updatePasswordTitle')"/>
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import LangSelect from '@/components/LangSelect'
import ChangePassword from '@/components/ChangePassword'
import useAppStore from '@/store/modules/app'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'
import {getCurrentInstance} from "vue";

const { proxy} = getCurrentInstance()
const changePasswordRef = ref()
const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

function toggleSideBar() {
  appStore.toggleSideBar()
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "logout":
      logout();
      break;
    case "changePassword":
      changePasswordRef.value.open(true);
      break;
    default:
      break;
  }
}

function logout() {
  ElMessageBox.confirm('确定退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index';
    })
  }).catch(() => { });
}

const emits = defineEmits(['setLayout'])
function setLayout() {
  emits('setLayout');
}

onMounted( () => {
    changePasswordRef.value.open(false);
})
</script>

<style lang='scss' scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #762ADB;
  // background: var(--color-primary);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  align-items: center;
  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;
    flex-shrink: 0;
    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .navbar-inner {
    flex: 1;
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    height: 100%;
    line-height: 50px;
    display: flex;
    padding-right: 17px;
    &:focus {
      outline: none;
    }

    &__logout {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-left: 25px;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 20px;
    }

    .right-menu-item {
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;
      display: flex;
      align-items: center;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }

    .avatar-wrapper {
      position: relative;
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      display: flex;
      align-items: center;
      .user-avatar {
        cursor: pointer;
        width: 28px;
        height: 28px;
        border-radius: 10px;
        margin-right: 9px;
      }

      i {
        cursor: pointer;
        position: absolute;
        right: -20px;
        top: 25px;
        font-size: 12px;
      }
    }
  }
}

:deep(.langSelect) {
    display: flex !important;
    align-items: center;
    font-size: 20px !important;
  }
</style>
