import hasRole from './permission/hasRole'
import {hasPermi, hasPermiEye} from './permission/hasPermi'
import copyText from './common/copyText'
import resubmit from './common/resubmit'

export default function directive(app){

  app.directive('hasRole', hasRole)
  app.directive('hasPermi', hasPermi)
  app.directive('hasPerm', hasPermi)
  app.directive('copyText', copyText)
  app.directive('resubmit', resubmit)
  app.directive("hasPermiEye", hasPermiEye)
}
