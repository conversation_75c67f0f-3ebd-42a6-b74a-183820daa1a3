<template>
    <div class="export-file" @click="handleExport">
        <slot :loading="loading"/>
        <span v-if=!useSlots().default>
            <el-button :loading="loading">导出</el-button>
        </span>
    </div>
</template>

<script setup>
import {PropType, useSlots} from "vue";
import {exportFileApi} from "@/api/common";
import utils from "@/utils/index.js";

const props = defineProps({
    modelValue: {
        type: String,
        default: "",
    },
    identifier: {  //标识符
        type: String,
        default: "",
    },
    fileName: {  //文件名称
        type: String,
        default: "template.xlsx",
    },
});

const emit = defineEmits(["update:modelValue", 'onSuccess']);

const state = reactive({
    loading: false,
})
const {
    loading,
} = toRefs(state);

/**
 *  导出
 */
function handleExport() {
    loading.value = true
    exportFileApi({}).then((res) => {
        ElMessage.success("导出成功");
        utils.exportFile(res, props.fileName)
        emit('onSuccess');
    }).catch(() => {
        ElMessage.error("导出失败");
    }).finally(() => {
        loading.value = false
    })
}

</script>

<style scoped lang="scss">
.export-file {
    display: inline;
}
</style>
