import { querySupplierList } from "@/api/common.js";

export function useSubData() {
  let subState = reactive({
    subTable: [
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: '<PERSON>', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: 'Tom', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: 'Tom', address: 'No. 189, Grove St, Los Angeles', },
      { date: '2016-05-03', name: 'Tom', address: 'No. 189, Grove St, Los Angeles', },
    ],
    subTableTotal: 0,
    subTableLoading: false,
    subParams: initSubParams(),
  })

  let { subTable, subTableLoading, subTableTotal, subParams } = toRefs(subState)

  const getSubTable = () => {
    // subTableLoading.value = true
    // querySupplierList(subParams.value).then(res => {
    //   subTable.value = res.data.records
    //   subTableTotal.value = res.data.total * 1
    // }).finally(() => subTableLoading.value = false)
  }

  function initSubParams () {
    return {
      page: 1,
      limit: 10,
    }
  }

  function resetSubParams() {
    subParams.value = initSubParams()
    handleSubQuery()
  }

  function handleSubQuery() {
    subParams.value.page = 1
    getSubTable()
  }


  return {
    subState,
    subTable,
    subTableTotal,
    subTableLoading,
    subParams,
    getSubTable,
    initSubParams,
    resetSubParams,
    handleSubQuery,
  }
}


