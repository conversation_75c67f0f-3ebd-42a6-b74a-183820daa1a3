import request from "@/utils/request.js";

// 待办提醒
export function todoList() {
    return request({
        url: '/odc-trade/workbench/todoList',
        method: 'get'
    })
}

//客户统计：饼图
export function customerAuthPieChart() {
    return request({
        url: '/odc-trade/workbench/customerAuthPieChart',
        method: 'get'
    })
}

// 客户认证趋势图
export function customerAuthTrends(data) {
    return request({
        url: '/odc-trade/workbench/customerAuthTrends',
        method: 'get'
    })
}

// 交易概览：销售额趋势图
export function salesTrends(data) {
    return request({
        url: '/odc-trade/workbench/salesTrends',
        method: 'post',
        data
    })
}

// 交易概览：销售金额
export function totalAmount(data) {
    return request({
        url: '/odc-trade/workbench/totalAmount',
        method: 'post',
        data
    })
}

// 原产地供应商家
export function supplierCountry(data) {
    return request({
        url: '/odc-trade/workbench/supplierCountry',
        method: 'get'
    })
}

// 获取角色快捷入口菜单
export function getHomeMenuList() {
    return request({
        url: '/base/home/<USER>/menu/list',
        method: 'get'
    })
}

// 获取登录用户已分配菜单权限
export function getAuthorityMenu() {
    return request({
        url: '/supply-base/oms/user/authority/menu',
        method: 'get'
    })
}

// 添加快捷入口菜单
export function addHomeMenu(data) {
    return request({
        url: '/base/home/<USER>/menu/add',
        method: 'get',
        params: data
    })
}
