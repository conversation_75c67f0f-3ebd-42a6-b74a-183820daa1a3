import { ref, reactive } from 'vue'
import {
  addTenant,
  getDetail,
  upadteTenant
} from "@/api/tenants.js";
import { ElMessage } from 'element-plus'
import { formatDate } from '@/utils/index.js'

export function useAddTenant() {
  const userForm = ref({
    businessLicense: "",
    contacts: "",
    endDate: "",
    mobile: "",
    startDate: "",
    systemTypes: ["supply"], // 默认选中系统设置
    tenantId: "",
    tenantName: "",
    tenantType: null,
    unitName: "",
    uscc: "",
    account: "保存后生成账号",
    password: "******"
  })

  const rules = {
    tenantName: [
      {
        required: true,
        message: "请输入租户名称",
        trigger: "blur",
      },
      {  max: 50, message: '租户名称长度不能超过50个字符', trigger: ['blur', 'change'] }
    ],
    tenantType: [
      {
        required: true,
        message: "请选择租户类型",
        trigger: "change",
      },
    ],
    contacts: [
      { required: true, message: '请输入联系人', trigger: 'blur' },
      { max: 30, message: '联系人长度不能超过30个字符', trigger: ['blur', 'change'] }
    ],
    mobile: [
      { required: true, message: '请输入手机号码', trigger: 'blur' },
      { max: 30, message: '手机号码长度不能超过30个字符', trigger: ['blur', 'change'] }
    ],
    unitName: [
      {
        required: true,
        message: "请输入单位名称",
        trigger: "blur",
      },
      {
        max: 50,
        message: "单位名称长度不能超过50个字符",
        trigger: ["blur", "change"],
      }
    ],
    uscc: [
      {
        required: true,
        message: "请输入统一社会信用代码",
        trigger: "blur",
      },
      { max: 30, message: '统一社会信用代码长度不能超过30个字符', trigger: ['blur', 'change'] }
    ],
    /* businessLicense: [  // 营业执照
      {
        required: true,
        message: "请上传营业执照",
        trigger: "change",
      }
    ], */
    businessLicense: [
      { required: true, message: '请上传营业执照', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (!value || value === '[]') {
            callback(new Error('请上传营业执照'))
          } else {
            callback()
          }
        },
        trigger: 'change'
      }
    ],
    systemTypes: [
      {
        required: true,
        message: "请选择系统类型",
        trigger: "change",
        type: 'array'
      },
    ],
    startDate: [
      {
        required: true,
        message: "请选择开始日期",
        trigger: "change",
      },
    ],
    endDate: [
      {
        required: true,
        message: "请选择结束日期",
        trigger: "change",
      },
    ],
  }

  const submitLoading = ref(false)

  // 处理表单提交
  const handleAddTenant = async () => {
    submitLoading.value = true
    try {
      const params = { ...userForm.value }
      delete params.account
      delete params.password
      params.startDate = (new Date(params.startDate)).getTime()
      params.endDate = (new Date(params.endDate)).getTime()
      params.businessLicense = JSON.stringify(params.businessLicense)
      const res = await addTenant(params)
      if (res.code === 0) {
        ElMessage.success('新增租户成功')
        return true
      }
      // ElMessage.error(res.msg || '新增租户失败')
      return false
    } catch (error) {
      console.error('Error adding tenant:', error)
      // ElMessage.error(error || '新增租户失败')
      return false
    } finally {
      submitLoading.value = false
    }
  }

  // 编辑租户
  const handleUpdateTenant = async () => {
    submitLoading.value = true
    try {
      const params = { ...userForm.value }
      delete params.account
      delete params.password
      params.startDate = (new Date(params.startDate)).getTime()
      params.endDate = (new Date(params.endDate)).getTime()
      params.businessLicense = JSON.stringify(params.businessLicense)
      const res = await upadteTenant(params)
      if (res.code === 0) {
        ElMessage.success('更新租户成功')
        return true
      }
      // ElMessage.error(res.msg || '更新租户失败')
      return false
    } catch (error) {
      console.error('Error updating tenant:', error)
      // ElMessage.error(error || '更新租户失败')
      return false
    } finally {
      submitLoading.value = false
    }
  }

  // 获取租户详情
  const handleLoad = async (tenantId) => {
    try {
      const res = await getDetail({ tenantId })
      if (res.code === 0) {
        // 处理日期格式
        const processedData = {
          ...res.data,
          startDate: formatDate(res?.data?.startDate || ''),
          endDate: formatDate(res?.data?.endDate || ''),
          password: "******"
        }
        Object.assign(userForm.value, processedData)
        userForm.value.businessLicense = JSON.parse(userForm.value.businessLicense)
      }
    } catch (error) {
      ElMessage.error('获取租户详情失败')
      console.error('接口调用异常:', error)
    }
  }

  // 重置表单
  const resetForm = () => {
    userForm.value = {
      businessLicense: "",
      contacts: "",
      endDate: "",
      mobile: "",
      startDate: "",
      systemTypes: [],
      tenantId: "",
      tenantName: "",
      tenantType: null,
      unitName: "",
      uscc: "",
    }
  }

  return {
    userForm,
    rules,
    submitLoading,
    handleAddTenant,
    resetForm,
    handleLoad,
    handleUpdateTenant
  }
}
