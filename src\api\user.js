/*
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-02 10:23:51
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-01-09 14:46:38
 * @FilePath: \supply-operation-web\src\api\user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from '@/utils/request'

export function login(data) {
  return request({
    url: '/supply-base/login/platformToken',
    method: 'post',
    data
  })
}

export function getInfo() {
  return request({
    url: '/supply-base/current/user',
    method: 'get',
  })
}

// 短信登录时发送的验证码
export function sendMessageCode(params) {
  return request({
    url: `/supply-base/oms/send/message/code/user/login`,
    method: 'get',
    params
  })
}
// 忘记密码时发送的验证码
export function resetPwdSendMessageCode(params) {
  return request({
    url: `/supply-base/oms/send/message/code/user/forget`,
    method: 'get',
    params
  })
}

export function logout(token) {
  return request({
    url: `/supply-base/logout/token`,
    method: 'delete',
    params: {
      token
    }
  })
}

export function queryUserAuthoritySidebarMenu(data) {
  return request({
    url: '/supply-base/current/user/menuTreeList?systemType=platform',
    method: 'get',
    params: data
  })
}

// 忘记密码-重置密码
export function resetPasswordBySMS(data) {
  return request({
    url: `/supply-base/oms/user/forgot/password`,
    method: 'post',
    data
  })
}

// 修改密码
export function updatePassword(data) {
  return request({
    url: `/supply-base/current/user/update/password`,
    method: 'post',
    data
  })
}
