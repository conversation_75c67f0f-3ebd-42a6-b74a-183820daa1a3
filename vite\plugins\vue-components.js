import path from 'path';
const pathSrc = path.resolve(__dirname, 'src')
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import IconsResolver from "unplugin-icons/resolver";
export default function createAutoImportComponents() {
    return Components({
        resolvers: [ElementPlusResolver(),IconsResolver({ enabledCollections: ["ep"] })],
        // 自定义解析目录
        deep: true,
        dirs: ["src/components"],
        dts: path.resolve(pathSrc, 'types/components.d.ts'),
    })
}
