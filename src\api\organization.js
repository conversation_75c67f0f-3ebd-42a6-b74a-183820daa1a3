import request from '@/utils/request'

export function getPageList(data) {
    return request({
        url: '/supply-base/role/page',
        method: 'post',
        data
    })
}

export function addRole(data) {
    return request({
        url: '/supply-base/role/add',
        method: 'post',
        data
    })
}

export function updateRole(data) {
    return request({
        url: '/supply-base/role/update',
        method: 'post',
        data
    })
}

// 启用禁用角色
export function updateEnableStatus(data) {
    return request({
        url: '/supply-base/role/updateEnableStatus',
        method: 'post',
        data
    })
}

// 编辑-获取角色所有菜单
export function roleMenuList(data) {
    return request({
        url: '/supply-base/oms/auth/role/menu/list',
        method: 'get',
        params: data
    })
}

// 新增-获取角色所有菜单
export function allMenuList(data) {
    return request({
        url: '/supply-base/role/authorityInfo',
        method: 'post',
        data
    })
}

// 权限设置 分配角色菜单
export function grantRoleMenu(data) {
    return request({
        url: '/supply-base/role/authorityInfo',
        method: 'post',
        data
    })
}

//  删除角色
export function delRole(data) {
    return request({
        url: '/supply-base/role/delete',
        method: 'get',
        params: data
    })
}

/**************账号管理*******************/
// 分页账号查询
export function queryUserListPage(data) {
    return request({
        url: '/supply-base/user/list/page',
        method: 'post',
        data
    })
}

// 更改账号状态/删除账号
export function updateUserStatus(data) {
    return request({
        url: '/supply-base/user/updateStatus',
        method: 'post',
        data
    })
}

// 重置密码
export function resetPassword(data) {
    return request({
        url: '/supply-base/user/resetPassword',
        method: 'post',
        data
    })
}

// 获取所有部门列表
export function allDeptList() {
    return request({
        url: '/supply-base/base/dept/tree',
        method: 'get',
    })
}

// 获取所有角色列表
export function allRoleList(data) {
    return request({
        url: '/supply-base/role/all',
        method: 'post',
        data
    })
}

// 添加账号
export function addUserInfo(data) {
    return request({
        url: '/supply-base/user/add',
        method: 'post',
        data
    })
}

// 编辑账号
export function updateUserInfo(data) {
    return request({
        url: '/supply-base/user/update',
        method: 'post',
        data
    })
}

// 获取国际国家列表-含港澳台
export function getAllCountry() {
    return request({
        url: `/supply-base/country/all`,
        method: "get",
    });
}

/**
 * 获取用户手机号(小眼睛查看手机号)
 *
 * @param queryParams 查询参数
 */
export function queryRealPhone(queryParams) {
    return request({
        url: `/supply-base/user/getPhoneNum`,
        method: "get",
        params: queryParams,
    });
}


