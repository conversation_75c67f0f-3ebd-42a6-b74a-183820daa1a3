<template>
  <el-form
    class="queryParamsForm"
    :model="userForm"
    :rules="rules"
    ref="accountRef"
    label-position="top"
    label-width="80px"
  >
    <div class="section-title">
      {{ $t("tenantsManagement.title.basicInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.tenantName')"
          prop="tenantName"
        >
          <el-input
            v-model="userForm.tenantName"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.tenantType')"
          prop="tenantType"
        >
          <el-select
            v-model="userForm.tenantType"
            :placeholder="$t('common.placeholder.selectTips')"
          >
            <el-option
              v-for="item in tenantTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.validityPeriod')"
          prop="startDate"
        >
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <div class="section-title">
      {{ $t("tenantsManagement.title.contactInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.contacts')"
          prop="contacts"
        >
          <el-input
            v-model="userForm.contacts"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.mobile')"
          prop="mobile"
        >
          <el-input
            v-model="userForm.mobile"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <div class="section-title">
      {{ $t("tenantsManagement.title.authInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.unitName')"
          prop="unitName"
        >
          <el-input
            v-model="userForm.unitName"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.uscc')"
          prop="uscc"
        >
          <el-input
            v-model="userForm.uscc"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.businessLicense')"
          prop="businessLicense"
        >
         <!--  <div><el-button type="primary" link  @click="fileInputRef.click()">文件上传</el-button></div>
          <div>支持png/jpg/jpeg/pdf格式上传，大小不超过10MB</div>
          <input style="display: none" ref="fileInputRef" type="file" @change="onChangeFile"></input> -->
          <UploadMultiple
            @update:model-value="onChangeMultiple"
            ref="detailPicsRef"
            :limit="1"
            :formRef="formRef"
            listType="text"
            fileSize="10"
            :fileType="filtType"
            class="modify-multipleUpload"
            v-model="userForm.businessLicense"
            name="detailPic">
            <!--               <template #file="{ file }">
                            图片展示
                          </template> -->
          </UploadMultiple>
        </el-form-item>
      </el-col>
    </el-row>

    <div class="section-title">
      {{ $t("tenantsManagement.title.platformAuth") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item
          :label="$t('tenantsManagement.label.platformPermissions')"
          prop="systemTypes"
        >
          <el-checkbox-group v-model="userForm.systemTypes">
            <!-- <el-checkbox label="oms">订单服务OMS</el-checkbox>
            <el-checkbox label="pms">采购服务PMS</el-checkbox>
            <el-checkbox label="wms">仓储服务WMS</el-checkbox>
            <el-checkbox label="tms">运输服务TMS</el-checkbox>
            <el-checkbox label="supply" disabled>系统设置</el-checkbox> -->
            <!-- <el-checkbox v-for="(item,index) in systemTypesList" :key="index" :label="item.code" :disabled="item.code=='supply'">
              {{ item.name }}
            </el-checkbox> -->

            <el-checkbox v-for="(item,index) in systemTypesList" :key="index" :label="item.code" :disabled="item.bindingTenantEnabledEdit=== false" :checked="item.bindingTenantEnabledEdit==false">
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="section-title">
      {{ $t("tenantsManagement.title.accountInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.accountLogin')"
          prop="account"
          required
        >
          <el-input disabled v-model="userForm.account"/>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.passwordLogin')"
          prop="password"
          required
        >
          <el-input disabled v-model="userForm.password"/>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script setup>
import { useAddTenant } from '../composables/useAddTenant'
import { commonUpload, previewSingle } from "@/utils/commonUpload.js";
const accountRef = ref(null)
const detailPicsRef = ref(null)
const { proxy } = getCurrentInstance();
const {
  userForm,
  rules,
  submitLoading,
  resetForm,
  handleAddTenant,
  handleLoad,
  handleUpdateTenant
} = useAddTenant()

const props = defineProps({
  tenantId: {
    type: String,
    required: true
  },
  editType: {
    type: String,
    required: true
  },
  systemTypesList:{
    type: Object,
    required: true
  }
})

const fileInputRef = ref(null)
const filtType = ["png", "jpg", "jpeg", "pdf"]
const onChangeFile = (e) => {
  const file = e.target.files[0]
  if (file) {
    userForm.value.businessLicense = file
    commonUpload(file, 'image').then(res => {
      userForm.value.businessLicense = res.url
    })
  }
}
// 处理表单提交
const handleSubmit = async () => {
  if (!accountRef.value) return false

  try {
    await accountRef.value.validate()
    try{
      if(props.editType === 'add'){
        return await handleAddTenant()
      }else if(props.editType === 'edit'){
        return await handleUpdateTenant()
      }
    }
    catch (err){
      return false
    }

  } catch (error) {
    return false
  }
}


// 处理文件上传
const onChangeMultiple = (val) => {
  // userForm.value.businessLicense = JSON.stringify(val)
  if (val && val.length > 0) {
    userForm.value.businessLicense = JSON.stringify(val)
    // 手动清除表单验证错误
    accountRef.value.clearValidate('businessLicense')
  } else {
    userForm.value.businessLicense = ''
  }
}

// 租户类型选项
const tenantTypeOptions = computed(() => [
  {
    label: proxy.$t('tenantsManagement.label.signedTenant'),
    value: 1
  },
  {
    label: proxy.$t('tenantsManagement.label.experienceTenant'),
    value: 2
  }
])

// 日期范围
const dateRange = computed({
  get() {
    if (userForm.value.startDate && userForm.value.endDate) {
      return [userForm.value.startDate, userForm.value.endDate]
    }
    return null
  },
  set(val) {
    if (val) {
      userForm.value.startDate = val[0]
      userForm.value.endDate = val[1]
    } else {
      userForm.value.startDate = ''
      userForm.value.endDate = ''
    }
  }
})

onMounted(() => {
  // 如果是编辑模式,则加载租户详情
  props.editType === 'edit' && props.tenantId && handleLoad(props.tenantId)
})

defineExpose({
  userForm,
  rules,
  submitLoading,
  handleSubmit,
  resetForm,
})
</script>

<style scoped lang="scss">
.section-title {
  margin: 20px 0;
  font-weight: bold;
}

:deep(.el-form-item) {
  display: flex;
  align-items: center;
}

:deep(.el-form-item__label) {
  margin-bottom: 0 !important;
}

:deep(.border-none) {
  .el-input__wrapper {
    border: none;
    box-shadow: none;
  }
  .el-select__wrapper {
    border: none;
    box-shadow: none;
  }
}
.modify-multipleUpload{
  max-width: 100%;
}
</style>
