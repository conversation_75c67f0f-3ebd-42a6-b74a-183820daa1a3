<template>
  <el-form-item label="供应商" prop="supplierId">
    <el-select v-model="computedVal" placeholder="请选择" clearable>
      <el-option v-for="(item, index) in supplierList" :key="item.id" :label="item.supplierName" :value="item.id" />
    </el-select>
  </el-form-item>
</template>
<script setup>
import { querySupplierList } from "@/api/common.js";
let supplierList = ref([])

let emit = defineEmits(['update:modelValue', 'update:supplierCode', 'update:supplierName', 'update:supplierArea'])
let props = defineProps({
  modelValue: [String, Number]
})
let computedVal = computed({
  set(value) {
    if (!value) {
      emit('update:modelValue', value)
      emit('update:supplierCode', undefined)
      emit('update:supplierName', undefined)
      emit('update:supplierArea', undefined)
      return
    }
    let supplierItem = supplierList.value.find(item => item.id === value)
    emit('update:modelValue', value)  // supplierId
    emit('update:supplierCode', supplierItem.supplierCode)
    emit('update:supplierName', supplierItem.supplierName)
    emit('update:supplierArea', supplierItem.countryName + supplierItem.cityName)
	},
  get() {
		return props.modelValue
	},
})

querySupplierList().then(res => {
  supplierList.value = res.data
})

</script>
<style scoped>

</style>
