<template>
  <el-form
    class="queryParamsForm"
    :model="userForm"
    :rules="rules"
    ref="accountRef"
    label-position="top"
    label-width="80px"
  >
    <div class="section-title">
      {{ $t("tenantsManagement.title.basicInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
          label-width="80px"
          :label="$t('tenantsManagement.label.tenantName')"
          prop="tenantName"
        >
          <el-input
            disabled="true"
            v-model="userForm.tenantName"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
        disabled="true"
          :label="$t('tenantsManagement.label.tenantType')"
          prop="tenantType"
        >
          <el-select
            disabled
            v-model="userForm.tenantType"
            :placeholder="$t('common.placeholder.selectTips')"
          >
            <el-option
              v-for="item in tenantTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.validityPeriod')"
          prop="startDate"
        >
          <el-date-picker
            disabled="true"
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <div class="section-title">
      {{ $t("tenantsManagement.title.contactInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.contacts')"
          prop="contacts"
        >
          <el-input
          disabled="true"
            v-model="userForm.contacts"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.mobile')"
          prop="mobile"
        >
          <el-input
          disabled="true"
            v-model="userForm.mobile"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
    </el-row>

    <div class="section-title">
      {{ $t("tenantsManagement.title.authInfo") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="8">
        <el-form-item
        disabled="true"
          :label="$t('tenantsManagement.label.unitName')"
          prop="unitName"
        >
          <el-input
            disabled="true"
            v-model="userForm.unitName"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item
          :label="$t('tenantsManagement.label.uscc')"
          prop="uscc"
        >
          <el-input
          disabled="true"
            v-model="userForm.uscc"
            :placeholder="$t('common.placeholder.inputTips')"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
          <el-form-item
            :label="$t('tenantsManagement.label.businessLicense')"
            prop="businessLicense"
          >
            <template v-if="businessLicensePreview?.url">
              <!-- 图片预览 -->
              <el-image
                v-if="isImageFile(businessLicensePreview.url)"
                :src="businessLicensePreview.url"
                :preview-src-list="[businessLicensePreview.url]"
                class="el-image-block"
              />
              <!-- PDF预览 -->
              <!--
              <iframe
                v-else-if="isPdfFile(businessLicensePreview.url)"
                :src="businessLicensePreview.url"
                style="width: 100%; height: 500px; border: none;"
              ></iframe>
              -->
              <el-link :href="businessLicensePreview.url" target="_blank" v-else>
                {{ businessLicensePreview.name }}
                </el-link>
            </template>
            <UploadMultiple v-if="false" @update:model-value="onChangeMultiple" ref="detailPicsRef" :limit="1" :formRef="formRef" class="modify-multipleUpload" name="detailPic">
              <template #default="{ file }">
                点击上传
              </template>
            </UploadMultiple>
          </el-form-item>
        </el-col>
    </el-row>

    <div class="section-title">
      {{ $t("tenantsManagement.title.platformAuth") }}
    </div>
    <el-row :gutter="20">
      <el-col :span="24">
        <el-form-item
          :label="$t('tenantsManagement.label.platformPermissions')"
          prop="systemTypes"
        >
          <el-checkbox-group v-model="userForm.systemTypes" disabled>
            <!-- <el-checkbox label="oms">订单服务OMS</el-checkbox>
            <el-checkbox label="pms">采购服务PMS</el-checkbox>
            <el-checkbox label="wms">仓储服务WMS</el-checkbox>
            <el-checkbox label="tms">运输服务TMS</el-checkbox>
            <el-checkbox label="supply" disabled>系统设置</el-checkbox> -->
            <el-checkbox v-for="(item,index) in systemTypesList" :key="index" :label="item.code" >
              {{ item.name }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
    <div class="section-title">
        {{ $t("tenantsManagement.title.accountInfo") }}
      </div>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item
            :label="$t('tenantsManagement.label.accountLogin')"
            prop="account"
            required
          >
            <el-input disabled v-model="userForm.account"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item
            :label="$t('tenantsManagement.label.passwordLogin')"
            prop="password"
            required
          >
           <el-input disabled v-model="userForm.password"/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-button type="primary" v-hasPermi="['sys:tenant:resetPassword']" @click="handleResetPassword(userForm)">{{
            $t("common.resetpassword")
          }}</el-button>
        </el-col>
      </el-row>
  </el-form>
</template>

<script setup>
import { useDetailTenant} from '../composables/useDetailTenant'
import { getDetail, resetPassword } from '@/api/tenants.js'
import { commonUpload, previewSingle } from "@/utils/commonUpload.js";
const accountRef = ref(null)
const {proxy} = getCurrentInstance();
const props = defineProps({
  formRef: {
    type: Object,
    required: true
  },
  tenantId: {
    type: String,
    required: true
  },
  systemTypesList:{
    type: Object,
    required: true
  }
})

const {
  userForm,
  handleLoad,
  // handleResetPassword
  businessLicensePreview
} = useDetailTenant()

function handleResetPassword(row) {
  ElMessageBox.confirm(proxy.$t('accountManagement.message.resetPasswordTips'), proxy.$t('common.tipTitle'), {
      confirmButtonText: proxy.$t('common.confirm'),
      cancelButtonText: proxy.$t('common.cancel'),
      type: "warning",
  }).then(() => {
      let params = {
        account : row.account,
        tenantId: props.tenantId
      }
      resetPassword(params).then(res => {
          if (res.code === 0) {
              ElMessage.success(proxy.$t('accountManagement.message.resertPasswordSucess'))
              getTableList()
          } else {
              ElMessage.success(proxy.$t('accountManagement.message.resertPasswordFail'))
          }
      })
  })
}

// 租户类型选项
const tenantTypeOptions = computed(() => [
  {
    label: proxy.$t('tenantsManagement.label.signedTenant'),
    value: 1
  },
  {
    label: proxy.$t('tenantsManagement.label.experienceTenant'),
    value: 2
  }
])

// 判断是否为图片文件
const isImageFile = (url) => {
  const imageExtensions = ['.png', '.jpg', '.jpeg', '.gif']
  return imageExtensions.some(ext => url.toLowerCase().includes(ext))
}

// 判断是否为PDF文件
const isPdfFile = (url) => {
  return url.toLowerCase().includes('.pdf')
}

// 日期范围
const dateRange = computed({
  get() {
    if (userForm.value.startDate && userForm.value.endDate) {
      return [userForm.value.startDate, userForm.value.endDate]
    }
    return null
  },
  set(val) {
    if (val) {
      userForm.value.startDate = val[0]
      userForm.value.endDate = val[1]
    } else {
      userForm.value.startDate = ''
      userForm.value.endDate = ''
    }
  }
})

onMounted(() => {
  handleLoad(props.tenantId)
})
defineExpose({
  userForm,
  handleLoad,
})
</script>

<style scoped lang="scss">
.section-title {
  margin: 20px 0;
  font-weight: bold;
}

:deep(.el-form-item) {
  display: flex;
  align-items: center;
}

:deep(.el-form-item__label) {
  margin-bottom: 0 !important;
}

:deep(.border-none) {
  .el-input__wrapper {
    border: none;
    box-shadow: none;
  }
  .el-select__wrapper {
    border: none;
    box-shadow: none;
  }
}
.el-image-block{
  width: 150px;
  height: 150px;
}
</style>
