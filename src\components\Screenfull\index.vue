<!--
 * @Author: <PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2025-01-10 09:14:18
 * @LastEditors: cheng<PERSON> <EMAIL>
 * @LastEditTime: 2025-03-17 17:31:02
 * @FilePath: \supply-operation-web\src\components\Screenfull\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <svg-icon :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'" @click="toggle" :style="{color:'#FFFFFF'}"/>
  </div>
</template>

<script setup>
import { useFullscreen } from '@vueuse/core'

const { isFullscreen, enter, exit, toggle } = useFullscreen();
</script>

<style lang='scss' scoped>
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #FFFFFF;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
</style>
