import request from "@/utils/request";
const FILE_BASE_URL = "/supply-base/aliyunOss/sts/yto";
// const { t } = useI18n();

class FileAPI {
  static getOssStsToken(params) {
    return request({
      url: `${FILE_BASE_URL}/token`,
      method: "get",
      params,
    });
  }

  // 文件批量查看
  static previewFile(data) {
    return request({
      url: `${FILE_BASE_URL}/previewFile`,
      method: "post",
      data,
    });
  }

  static previewFileNew(data) {
    /*  {
      "appId": "",
      "customUrlCode": "supplyUrlCode",
      "expireCode": "",
      "fileUrlType": "EXTERNAL_NETWORK",
      "searchList": [
        {
          "bucket": "yt-oxms-uat",
          "fileName": "yt-oxms-uat/image/7ae5aa7f-17f3-47c3-8819-df9ff3fa60e4.zip",
          "objectId": "",
          "process": "",
          "responseHeaders": {
            "cacheControl": "",
            "contentDisposition": "",
            "contentEncoding": "",
            "contentLangauge": "",
            "contentType": "",
            "expires": ""
          }
        }
      ],
      "viewIp": ""
    } */
    const postData = {
      appId: "",
      customUrlCode: "supplyUrlCode",
      expireCode: "",
      fileUrlType: "EXTERNAL_NETWORK",
      searchList: data,
    };
    return request({
      url: `${FILE_BASE_URL}/previewFileNew`,
      method: "post",
      data: postData,
    });
  }

  static getSignedUrl(data) {
    return request({
      url: `${FILE_BASE_URL}/generateSignedUrl`,
      method: "post",
      data,
    });
  }

  /**
   * 下载文件
   * @param url
   * @param fileName
   */
  static downloadFile(url, fileName) {
    return request({
      url: url,
      method: "get",
      responseType: "blob",
    }).then((res) => {
      const blob = new Blob([res.data]);
      const a = document.createElement("a");
      const url = window.URL.createObjectURL(blob);
      a.href = url;
      a.download = fileName || "下载文件";
      a.click();
      window.URL.revokeObjectURL(url);
      // ElMessage.success(t('exportSequence.message.downLoadSuccess'));
    });
  }
}

export default FileAPI;
