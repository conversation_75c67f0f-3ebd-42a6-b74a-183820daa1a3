import OSS from 'ali-oss';
import { getOssStsToken, getYtoOssStsToken, queryOssImageByFileName } from './api';
import {getOssImageUrls, setOssDomin} from './tranformOssImage.js';
import * as utils from './utils.js'
import { parseDateTime } from '@/utils/index'
let credentials = null; // STS凭证
let privateCredentials = null // 隐私文件STS凭证
let ossClient = null; // oss客户端实例
let privateOssClient = null //隐私文件oss客户端实例
let bucket = ''; // bucket名称
let region = 'oss-cn-shanghai'; // oss服务区域名称

let isRefreshingToken = false;
let requestQueue = []
// 获取STS Token
export async function getCredential (type = '') {
  if (!isRefreshingToken) {
    isRefreshingToken = true;
    return getYtoOssStsToken({type:type}).then(res => {

      if(type === 'PRIVATE'){
        privateCredentials = res.data;
      }else{
        credentials = res.data;
        bucket = res.data.bucketName;
      }
      // setOssDomin(credentials.upUrl)
      initOSSClient(type);
      requestQueue.forEach(cb => cb())
      requestQueue = []
    }).catch(err => {
      console.log('err', err);
      requestQueue = []
    }).finally(() => {
      isRefreshingToken = false
    })
  } else {
    return new Promise(resolve => requestQueue.push(() => resolve()))
  }
}

export async function getBucket() {
  if (bucket) {
    return Promise.resolve(bucket)
  } else {
    if (!ossClient) {
      await getCredential();
    }
    return bucket
  }
}

// 创建OSS Client
function initOSSClient (type) {
  if(type === 'PRIVATE'){
    let { accessKeyId, securityToken} = privateCredentials;
    privateOssClient = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: privateCredentials.secretAccessKey || privateCredentials.accessKeySecret,
      stsToken: securityToken,
      secure:true,
      bucket: bucket,
      region
    });
  }else{
    let { accessKeyId, securityToken } = credentials;
    ossClient = new OSS({
      accessKeyId: accessKeyId,
      // accessKeySecret: credentials.secretAccessKey || credentials.accessKeySecret,
      accessKeySecret: credentials.secretAccessKey || credentials.secretAccessKey,
      stsToken: securityToken,
      secure:true,
      bucket:bucket,
      region
    });
  }

}
/*
 * 临时上传 文件地址会过期
 * type为folder的子文件夹，主要为了便于设置不同文件类型的失效时间，取值为image, apk, doc, excel
 * isPrivate :是否为敏感文件 ‘’ 为否  ‘PRIVATE’ 为是
*/
export async function commonUpload (file, type = 'omsImage', isPrivate = '') {
  if(isPrivate === 'PRIVATE'){
    if (!privateOssClient) {
      await getCredential(isPrivate);
    }
    let nowTime = new Date().getTime();
    let expiration = privateCredentials.expiration ? new Date(privateCredentials.expiration).getTime() / 1000 : privateCredentials.expirationTime;
    let expirationTime = expiration * 1000;

    if (nowTime > expirationTime) {
      privateOssClient && privateOssClient.cancel();
      await getCredential(isPrivate);
    }
    const fileType = file.name.split('.')[file.name.split('.').length-1]
    const uuid = utils.getUuid()
    const fileName =  file.name;
    let response = await privateOssClient.put(`/${privateCredentials.folder}/${parseDateTime(nowTime)}/${parseDateTime(nowTime,'folderDateTime')}/${type}/${fileName}`, file);
    response.url = decodeURIComponent(response.url) // 处理中文名被转义问题
    console.log(response)
    return response;
  }else{
    if (!ossClient) {
      await getCredential(isPrivate);
    }
    let nowTime = new Date().getTime();
    let expiration = credentials.expiration ? new Date(credentials.expiration).getTime() / 1000 : credentials.expirationTime;
    let expirationTime = expiration * 1000;
    if (nowTime > expirationTime) {
      ossClient && ossClient.cancel();
      await getCredential(isPrivate);
    }
    const fileType = file.name.split('.')[file.name.split('.').length-1]
    const uuid = utils.getUuid()
    const fileName =  file.name;
    let response = await ossClient.put(`/${credentials.folder}/${parseDateTime(nowTime)}/${parseDateTime(nowTime,'folderDateTime')}/${type}/${fileName}`, file);
    response.url = decodeURIComponent(response.url) // 处理中文名被转义问题
    getOssImageUrls(response.url, { imageProp: ['url'] })
    console.log(response)
    return response;
  }

}
