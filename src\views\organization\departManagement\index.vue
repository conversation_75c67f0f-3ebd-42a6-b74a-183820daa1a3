<template>
  <div class="page departManagement">
    <div class="queryParamsForm">
      <el-form :inline="true" v-model="searchForm">
        <el-form-item :label="'部门名称'">
          <el-input type="text" placeholder="请输入" v-model.trim="searchForm.deptName" clearable></el-input>
        </el-form-item>
        <el-form-item class="btn-form">
          <el-button type="primary" @click="searchEvent">搜索</el-button>
          <el-button @click="resetEvent">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-info">
      <div class="mb10">
        <el-button @click="openAdd" type="primary">新增部门</el-button>
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        stripe
        row-key="id"
        :tree-props="{
          children: 'children',
          hasChildren: 'hasChildren'
        }"
        style="width: 100%">
        <el-table-column prop="deptName" label="部门名称"></el-table-column>
        <el-table-column prop="deptUserCount" label="员工数"></el-table-column>
        <el-table-column prop="deptLevel" label="部门类型">
          <template #default="scope">
            <span>{{
              scope.row.deptLevel === 1 ? '一级部门' :
              scope.row.deptLevel === 2 ? '二级部门' :
              scope.row.deptLevel === 3 ? '三级部门' :
              '未知级别'
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间">
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.updateTime, 'dateTime') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="scope">
            <el-button
              v-if="scope.row.deptLevel !== 3"
              type="primary"
              link
              @click="handleAdd(scope.row)">新增</el-button>
            <el-button type="primary" link @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="danger" link @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        <template #empty>
        <Empty/>
      </template>
      </el-table>
      <pagination
        v-show="tableTotal > 0"
        :total="tableTotal"
        v-model:page="searchForm.page"
        v-model:limit="searchForm.limit"
        @pagination="getTableList"
      />
    </div>
    <Edit
      v-model="deptDialog.open"
      ref="cmtDept"
      :title="deptDialog.title"
      @submitted="submitDept"
    />
  </div>
</template>

<script setup name="departManagement">
import Edit from './edit.vue'
import {addDept, deleteDept, getDeptDetail, getDepts, updateDept, getDeptTree} from '@/api/dept.js'
import {ElMessage, ElMessageBox} from "element-plus";

const {proxy} = getCurrentInstance()

const data = reactive({
  searchForm: initForm(),
  tableLoading: false,
  tableTotal: 0,
  tableData: [],
})

const deptDialog = ref({
  open: false,
  title: ''
})

const {searchForm, tableData, tableTotal, tableLoading} = toRefs(data)

function initForm() {
  return {
    deptName: '',
    page: 1,
    limit: 20,
  }
}

function getTableList() {
  tableLoading.value = true
  getDepts(searchForm.value)
    .then(res => {
      if (res.code === 0) {
        tableData.value = res.data
        // tableTotal.value = parseInt(res.data.total)
      }
    })
    .finally(() => tableLoading.value = false)
}

function searchEvent() {
  searchForm.value.page = 1
  getTableList()
}

function resetEvent() {
  searchForm.value = initForm()
  getTableList()
}

const cmtDept = ref(null)

function openAdd() {
  cmtDept.value.setEditType("add")
  cmtDept.value.setDeptLevelStatus(true)
  deptDialog.value.open = true
  deptDialog.value.title = '新增部门'
}

function handleAdd(row) {
  cmtDept.value.setEditType("add")
  cmtDept.value.setDeptLevelStatus(true)
  cmtDept.value.setDeptTreeStatus(true)
  cmtDept.value.setParentDept(row)
  deptDialog.value.open = true
  deptDialog.value.title = '新增部门'
}

function handleEdit(row) {
  cmtDept.value.setEditType("edit")
  cmtDept.value.setDeptLevelStatus(true)
  cmtDept.value.setDeptTreeStatus(true)
  cmtDept.value.setParentDept(row, 'edit')
  cmtDept.value.setFormData(row)
  deptDialog.value.open = true
  deptDialog.value.title = '编辑部门'
}

function handleDelete(row) {
  ElMessageBox.confirm('确定要删除该部门吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteDept(row.id).then(res => {
      if (res.code === 0) {
        ElMessage.success('删除成功')
        searchEvent()
      }
    })
  })
}

function submitDept() {
  searchEvent()
}

// 初始加载
searchEvent()
</script>

<style scoped lang="scss">
.departManagement {
  :deep(.el-dialog) {
    padding: 0 !important;
  }

  :deep(.el-dialog__header) {
    width: 100%;
    padding: 16px 32px;
    box-sizing: border-box;
    border-bottom: 1px solid #ECEEF1;
  }

  :deep(.el-dialog__body) {
    padding: 32px;
  }

  :deep(.el-dialog__footer) {
    padding-bottom: 12px;
    padding-right: 32px;
    text-align: right;
  }
}
</style>
