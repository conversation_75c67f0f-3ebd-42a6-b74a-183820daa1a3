<template>
  <div class="loginPage">
    <div class="login-welcome">
      <p class="login-welcome__text">welcome！</p>
      <span class="login-welcome__title">{{ data.title }}</span>
    </div>
    <div class="login">
      <img src="~@/assets/images/login-left.png" class="login-left">
      <div class="login-container">
        <div style="padding: 0 420px;">
          <el-row class="login-container__hd" type="flex" justify="start" align="middle">
            <span class="login-container__title">重置密码</span>
          </el-row>
          <div class="login-bd">
            <el-form ref="elFormRef" class="login-form" :model="data.form" :rules="data.rules" label-width="0">
              <el-form-item label="" prop="userName">
                <el-input v-model="data.form.userName" placeholder="请输入手机号" clearable maxlength="11"></el-input>
              </el-form-item>
              <el-form-item label="" prop="msgcode">
                <el-row type="flex" style="width: 100%;">
                  <el-input v-model="data.form.msgcode" placeholder="验证码" name="msgcode" style="flex: 1;" tabindex="2" auto-complete="on" />
                  <span>
                    <el-button v-show="!data.countDown" class="countDown" @click="showVerify" type="primary" link size="small">获取验证码</el-button>
                    <el-button v-show="data.countDown" class="countDown is-wait" type="primary" link size="small">
                      {{ data.countDown }}秒后可重发
                    </el-button>
                  </span>
                </el-row>
              </el-form-item>
              <el-form-item label="" prop="password">
                <el-input type="password" v-model="data.form.password" @keyup.enter.native="onClickConfirm" placeholder="请输入新密码" show-password maxlength="20" clearable></el-input>
              </el-form-item>
              <el-form-item label="" prop="confirmPassword">
                <el-input type="password" v-model="data.form.confirmPassword" @keyup.enter.native="onClickConfirm" placeholder="请确认新密码" show-password maxlength="20" clearable></el-input>
              </el-form-item>
              <el-button class="submit-button" :loading="data.submitLoading" type="primary" @click.native.prevent="onClickConfirm">确认修改并登录</el-button>
            </el-form>
          </div>
        </div>
      </div>
    </div>
    <Verify @success="onSuccessVerify" :mode="data.verifyOptions.mode" :vSpace="16" :captchaType="data.verifyOptions.captchaType"
      :imgSize="data.verifyOptions.imgSize" ref="verifyRef" />
  </div>
</template>
<script setup>
import { resetPasswordBySMS, resetPwdSendMessageCode } from "@/api/user";
import Verify from '@/components/verifition/Verify.vue'
import useUserStore from '@/store/modules/user'
import { useRouter } from 'vue-router'
import { setToken } from '@/utils/auth'


let userStore = useUserStore()

const router = useRouter()
const { proxy: $vm } = getCurrentInstance()

const emit = defineEmits(['dialogVisible'])

const elFormRef = ref(null)
const verifyRef = ref(null)

var validateConfirmPassword = (rule, value, callback) => {
  if (value !== data.form.password) {
    callback(new Error('确认密码与新密码不一致'));
  } else {
    callback();
  }
}

const data = reactive({
  title: import.meta.env.VITE_APP_TITLE,
	countDown: '',
	verifyOptions: {
		captchaType: 'blockPuzzle',
		mode: 'pop',
		imgSize: { width: '400px', height: '180px' },
	},
	form: initForm(),
	rules: {
		userName: [
			{ required: true, message: '请输入手机号', trigger: 'change' },
			{
				pattern: /^[1][3,4,5,6,7,8,9][0-9]{9}$/,
				message: '请输入正确的手机号',
				trigger: ['blur', 'change']
			},
		],
		msgcode: [
			{ required: true, message: '请输入验证码', trigger: 'change' },
		],
		password: [
			{ required: true, message: '请输入新密码', trigger: 'change' },
      { min: 8, max: 20, message: "长度在8到16个字符", trigger: "change" },
	  { pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Za-z])(?=\S*[!@$%^&_=.])\S*$/, message: $vm.$t('changePassword.rules.passwordFomart'), trigger: 'change' },
		],
		confirmPassword: [
			{ required: true, message: '请输入确认密码', trigger: 'change' },
			{ required: true, validator: validateConfirmPassword, trigger: 'change' }
		],
	},
	submitLoading: false,
  redirect: undefined
})

function reset() {
	data.form = initForm()
	nextTick(() => {
		$vm.$refs['elFormRef'].clearValidate()
		$vm.$refs['elFormRef'].resetFields()
	})
}

function initForm() {
	return {
		userName: undefined,
		msgcode: undefined,
		password: undefined,        //	新密码
		confirmPassword: undefined, //	确认新密码
    systemType: 'odc_manage',   // 系统类型:odc_trade:直采交易 odc_manage:直采管理 odc_otcs:溯源系统 zheshang:浙商系统
	}
}

function showVerify() {
	elFormRef.value.validateField('userName', (errMsg) => {
		if (!errMsg) return;
		verifyRef.value.show()
	})
}

let timer;
let captchaVO;
onBeforeUnmount(() => {
	clearInterval(timer);
	timer = null;
})

function onSuccessVerify(params) {
	console.log(params);
	captchaVO = {
		...data.verifyOptions,
		...params,
	}
	const TIME_COUNT = 60
	  resetPwdSendMessageCode({
      mobile: data.form.userName,
			// captchaVO,
		})
		.then(res => {
			if (!timer) {
				data.countDown = TIME_COUNT;
				timer = setInterval(() => {
					if (data.countDown > 0 && data.countDown <= TIME_COUNT) {
						data.countDown--;
					} else {
						clearInterval(timer);
						timer = null;
					}
				}, 1000)
			}
			$vm.$message.success('发送成功')
		})
}

function onClickConfirm() {
	/* Warn: Cannot find refs name */
	$vm.$refs['elFormRef'].validate(valid => {
		if (!valid) return
		data.submitLoading = true
		resetPasswordBySMS({
				...data.form,
				captchaVO: captchaVO,
			})
			.then(res => {
				console.log('then', res);
				userStore.setAppToken(res.data.access_token)
				router.push({ path: '/' })
			})
			.catch(() => {
        captchaVO = null
        data.submitLoading = false
      })
	})
}
</script>
<style lang="scss" scoped>
@import "./common.scss";


.login-container__hd {
  padding-left: 40px;
  padding-right: 40px;
}
</style>
