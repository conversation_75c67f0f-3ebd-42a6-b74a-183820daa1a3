import request from '@/utils/request'
/**
 * 上传文件
 *
 * @param file
 */
export function uploadFileApi(file) {
  const formData = new FormData();
  formData.append("file", file);
  return request({
    url: "/file/upload",
    method: "post",
    data: formData,
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
}

/**
 * 获取城市列表（不含港澳台）
 */
export const getCountryList = () => {
  return request({
    url: '/base/odc/country/list',
    method: 'get'
  })
}
/**
 * 获取含有港澳台
 * @returns {*}
 */
export const getAllCountryList = () => {
  return request({
    url: '/base/odc/country/all',
    method: 'get'
  })
}
/**
 * 根据国家获取城市列表
 */
export const getCityListByCountryId = (countryId) => {
  return request({
    url: '/base/odc/city/list',
    method: 'get',
    params: {
      countryId
    }
  })
}

/**
 * 获取服务条款（不分页）
 */
export const getServiceTermList = () => {
  return request({
    url: '/odc-goods/serviceTerms/queryList',
    method: 'get'
  })
}
/**
 * 获取所有银行
 */
export const getBankList = () => {
  return request({
    url: '/base/bank/list',
    method: 'post',
    data: {}
  })
}


/**
 * 货物总件数单位
 */
export const queryCargoQtyUnit = () => {
  return request({
    url: '/base/dict/cargoQtyUnit',
    method: 'get',
  })
}

/**
 * 货物分类
 */
export const queryCargoTypeCode = () => {
  return request({
    url: '/base/dict/cargoTypeCode',
    method: 'get',
  })
}

/**
 * 查询字典集合
 */
export const queryDictValue = (data) => {
  return request({
    url: '/base/dict/get/value/By/key',
    method: 'get',
    params: data
  })
}


// 国家-机场
let airportTree = []
export const queryAirportTree = () => {
  if (airportTree.length > 0) return Promise.resolve(airportTree)
  return request({
    url: '/base/odc/airport/tree',
    method: 'get',
  }).then(res => {
    airportTree = res.data
    return airportTree
  })
}

// 查询所有客户
export const queryCustomList = (data) => {
  return request({
    url: '/supply-base/oms/user/enterprise/all/list',
    method: 'get',
    params: data
  })
}

// 查询用户下载历史
export const queryExportList = (data) => {
  return request({
    url: '/base/export/page',
    method: 'get',
    params: data
  })
}

// 修改下载状态为已下载
export const updateExportItemStatus = (data) => {
  return request({
    url: '/base/export/update',
    method: 'get',
    params: data
  })
}


//供应商下拉列表
export function querySupplierList(data) {
  return request({
    url: '/supply-base/oms/supplier/find/by/name/or/code',
    method: 'get',
    params: data
  })
}

// 原产地供应商
export const queryAllSupplierList = (data) => {
  return request({
    url: '/supply-base/oms/supplier/all/list',
    method: 'get',
    params: data
  })
}

// 获取私密文件地址
export function getSignedUrl(params) {
  return request({
    url: '/supply-base/aliyunOss/sts/generateSignedUrl',
    method: 'post',
    data: params
  })
}

// 查询仓库列表 warehouseType 仓库类型 1原料仓 2成品仓
export function queryWarehouseList(params) {
  return request({
    url: '/odc-goods/warehouseInfo/queryList',
    method: 'post',
    data: params
  })
}
