import autoImport from 'unplugin-auto-import/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';
import IconsResolver from "unplugin-icons/resolver";
export default function createAutoImport() {
    return autoImport({
        imports: [
            'vue',
            'vue-router',
            'pinia',
            'vue-i18n'
        ],
        resolvers: [
            ElementPlusResolver(),
            IconsResolver({
            prefix: 'Icon',
        })
        ],
        dts: false
    })
}
