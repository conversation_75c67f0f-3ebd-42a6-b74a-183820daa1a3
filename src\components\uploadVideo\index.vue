<template>
<div class="upload-video">
  <el-upload v-loading="loading" element-loading-text="上传中" element-loading-spinner="el-icon-loading" action=""
             :drag="drag" v-bind="$attrs" :accept="accept" :auto-upload="true" class="upload-block"
             :before-upload="handleBeforeUpload" :http-request="uploadImage" :on-success="handleUploadSuccess"
             :on-error="handleUploadError" :show-file-list="false" style="display: inline-block; vertical-align: top;"
             :style="{ width, height }">
    <div class="handle-upload" style="line-height: 20px;" v-if="!imgUrl">
      <slot>
        <i class="el-icon-upload"></i>
        <div class="el-upload__text"><em>点击</em>或将视频<em>拖拽</em>到此处上传</div>
        <div style="color: #606266;font-size: 12px;">
          仅支持mp4格式，大小不超过{{fileSize}}M
        </div>
      </slot>
    </div>
    <div v-else class="file-block">
      <video class="file-block__video" controls :src="imgUrl" disablepictureinpicture
             controlsList="noplaybackrate"></video>
      <div class="file-block__actions">
          <span title="移除" @click.stop="handleRemove">
             <i-ep-Delete color="#fff"/>
          </span>
      </div>
    </div>
  </el-upload>
  <span v-if="showUrl">{{imgUrl}}</span>
</div>
</template>
<script setup lang="ts" name="uploadVideoIndex">
import {reactive, toRefs} from "vue";
import { commonUpload } from "@/api/oss";
import {useVModel} from "@vueuse/core";

const emit = defineEmits(['update:modelValue'])
const props = defineProps({
  modelValue: {
    type: String,
    default: ""
  },

  drag: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: "video/mp4"
  },
  // 文件大小
  fileSize: {
    type: Number,
    default: 3 // 单位M
  },
  width: {
    type: String,
    default: "330px"
  },
  height: {
    type: String,
    default: "180px"
  },
  formRef: {
    type: Object,
    default: () => {
    }
  },
  name: {
    type: String,
    default: ''
  },
  showUrl: {
    type: Boolean,
    default: false
  }
})
const imgUrl = useVModel(props, "modelValue", emit);
const state = reactive({
  loading: false,
})
const {
  loading,
} = toRefs(state)
/**
 * @param options
 */
async function uploadImage(options: UploadRequestOptions): Promise<any> {
  loading.value = true
  await commonUpload(options.file).then(res => {
    console.log(res.url+"============================url");
    imgUrl.value = res.url;
    props.formRef.validateField(props.name)
  }).finally(() => {
    props.value = '';
    loading.value = false
  })

}
function handleRemove(){
  imgUrl.value = '';
  emit("update:modelValue", "");
}
function handleUploadSuccess(){
  emit("update:modelValue", imgUrl.value);
}
function handleBeforeUpload(file:UploadRawFile){
  const isLimitType = ["video/mp4"].includes(file.type);
  if (!isLimitType) {
    ElMessage.error(`视频仅支持mp4格式`);
    return false;
  }
  const fileSize = file.size / 1024 / 1024 <= props.fileSize;
  if (!fileSize) {
    ElMessage.error(`文件大小不能超过${props.fileSize}M!`);
    return false;
  }
  loading.value = true;
  return isLimitType && fileSize;
}
function handleUploadError(res) {
  console.log("handleUploadError", res);
  ElMessage.error(`上传失败`)
}
</script>
<style scoped lang="scss">
.upload-video {
  .upload-block {
    :deep(.el-upload--text) {
      width: 100%;
      height: 100%;
    }

    :deep(.el-upload-dragger) {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    :deep(.modify-elIcon) {
      color: #efefef;
      font-size: 20px;
      vertical-align: top;
    }

    .file-block {
      position: relative;
      width: 100%;
      height: 100%;

      &__video {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      &__actions {
        position: absolute;
        top: 0;
        right: 0;
        padding: 12px 12px;
        line-height: 1;
      }
    }
  }
  .file-block__actions{
    z-index: 100;
    top: -2px !important;
    right: -2px !important;
  }
}
</style>
