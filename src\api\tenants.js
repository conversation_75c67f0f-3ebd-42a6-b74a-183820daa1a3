import request from "@/utils/request";

export function addTenant(data) {
  // 新增租户
  return request({
    url: "/supply-base/tenant/add",
    method: "post",
    data,
  });
}

export function deleteTenant(data) {
  // 删除租户
  return request({
    url: "/supply-base/tenant/delete",
    method: "post",
    data,
  });
}
export function getDetail(params = {}) {
  // 获取租户详情
  return request({
    url: "/supply-base/tenant/detail",
    method: "get",
    params,
  });
}

//
export function allList(query = {}) {
  // 获取租户列表不分页
  return request({
    url: "/supply-base/tenant/list",
    method: "get",
    query,
  });
}

export function pageList(params = {}) {
  // 获取租户列表分页
  return request({
    url: "/supply-base/tenant/page",
    method: "get",
    params,
  });
}

export function upadteTenant(data) {
  // 编辑租户
  /*{
    "businessLicense": "",
    "contacts": "",
    "endDate": "",
    "mobile": "",
    "startDate": "",
    "systemTypes": [],
    "tenantId": "",
    "tenantName": "",
    "tenantType": 0,
    "unitName": "",
    "uscc": ""
  }*/
  return request({
    url: "/supply-base/tenant/update",
    method: "post",
    data,
  });
}

export function updateStatus(data) {
  // 更改租户状态
  /* {
    "status": 0,
    "tenantId": ""
  }*/
  return request({
    url: "/supply-base/tenant/updateStatus",
    method: "post",
    data,
  });
}
export function resetPassword(data) {
  // 重置密码
  /*{
    "account": ""
  }*/
  return request({
    url: "/supply-base/tenant/resetPassword",
    method: "post",
    data,
  });
}

export function getPhoneNum(params = {}) {
  // tenantId
  // 获取租户手机号(小眼睛查看手机号
  return request({
    url: "/supply-base/tenant/getPhoneNum",
    method: "get",
    params,
  });
}

export function queryTenantSystemTypeList(){
  return request({
    url: "/supply-base/systemType/queryTenantSystemTypeList",
    method: "get",
  });
}
