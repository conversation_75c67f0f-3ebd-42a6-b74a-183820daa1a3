 /**
 * 通用css样式布局处理
 */

 /** 基础通用 **/
.pt5 {
	padding-top: 5px;
}
.pr5 {
	padding-right: 5px;
}
.pb5 {
	padding-bottom: 5px;
}
.mt5 {
	margin-top: 5px;
}
.mr5 {
	margin-right: 5px;
}
.mb5 {
	margin-bottom: 5px;
}
.mb8 {
	margin-bottom: 8px;
}
.ml5 {
	margin-left: 5px;
}
.mt10 {
	margin-top: 10px;
}
.mr10 {
	margin-right: 10px;
}
.mb10 {
	margin-bottom: 10px;
}
.ml10 {
	margin-left: 10px;
}
.mt20 {
	margin-top: 20px;
}
.mr20 {
	margin-right: 20px;
}
.mb20 {
	margin-bottom: 20px;
}
.ml20 {
	margin-left: 20px;
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
	font-family: inherit;
	font-weight: 500;
	line-height: 1.1;
	color: inherit;
}

.el-form .el-form-item__label {
  font-family: Ping<PERSON>angSC, PingFang SC;
  font-weight: 500;
  font-size: 14px;
  color: #52585F;
  line-height: 20px;
  text-align: left;
  font-style: normal;
}

.el-dialog.scrollbar .el-dialog__body {
	overflow: auto;
	overflow-x: hidden;
	max-height: 70vh;
	padding: 10px 20px 0;
}

.el-table {
	.el-table__header-wrapper, .el-table__fixed-header-wrapper {
		th {
			word-break: break-word;
			background-color: #F3F5F9 !important;
			color: #515a6e;
			height: 40px !important;
			font-size: 13px;
		}
	}
	.el-table__body-wrapper {
		.el-button [class*="el-icon-"] + span {
			margin-left: 1px;
		}
	}
  .cell:empty::before{
    content: '-';
  }
 .el-table__row--striped td.el-table__cell {
    background: #F3F5F9 !important;
}
}

/** 表单布局 **/
.form-header {
    font-size:15px;
	color:#6379bb;
	border-bottom:1px solid #ddd;
	margin:8px 10px 25px 10px;
	padding-bottom:5px
}

/** 表格布局 **/
.pagination-container {
	position: relative;
	margin-top: 20px;
	padding: 10px 20px;
}

.el-dialog .pagination-container {
	position: static !important;
}

/* tree border */
.tree-border {
    margin-top: 5px;
    border: 1px solid #e5e6e7;
    background: #FFFFFF none;
    border-radius:4px;
    width: 100%;
}

@media ( max-width : 768px) {
  .pagination-container .el-pagination > .el-pagination__jump {
    display: none !important;
  }
  .pagination-container .el-pagination > .el-pagination__sizes {
    display: none !important;
  }
}

.el-table .fixed-width .el-button--small {
	padding-left: 0;
	padding-right: 0;
	width: inherit;
}

/** 表格更多操作下拉样式 */
.el-table .el-dropdown-link {
	cursor: pointer;
	color: #409EFF;
	margin-left: 10px;
}

.el-table .el-dropdown, .el-icon-arrow-down {
	font-size: 12px;
}

.el-tree-node__content > .el-checkbox {
	margin-right: 8px;
}

.list-group-striped > .list-group-item {
	border-left: 0;
	border-right: 0;
	border-radius: 0;
	padding-left: 0;
	padding-right: 0;
}

.list-group {
	padding-left: 0px;
	list-style: none;
}

.list-group-item {
	border-bottom: 1px solid #e7eaec;
	border-top: 1px solid #e7eaec;
	margin-bottom: -1px;
	padding: 11px 0px;
	font-size: 13px;
}

.pull-right {
	float: right !important;
}

.el-card__header {
	padding: 14px 15px 7px !important;
	min-height: 40px;
}

.el-card__body {
	padding: 15px 20px 20px 20px !important;
}

.card-box {
	padding-right: 15px;
	padding-left: 15px;
	margin-bottom: 10px;
}

/* button color */
.el-button--cyan.is-active,
.el-button--cyan:active {
  background: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

.el-button--cyan:focus,
.el-button--cyan:hover {
  background: #48D1CC;
  border-color: #48D1CC;
  color: #FFFFFF;
}

.el-button--cyan {
  background-color: #20B2AA;
  border-color: #20B2AA;
  color: #FFFFFF;
}

 .el-button--text{
   color: #D01426 !important;
 }
 .el-button--text:hover {
   color: #d7616c !important;
 }
 .el-button--text:active {
   color: #D01426 !important;
 }

/* text color */
.text-navy {
	color: #1ab394;
}

.text-primary {
	color: inherit;
}

.text-success {
	color: #1c84c6;
}

.text-info {
	color: #23c6c8;
}

.text-warning {
	color: #f8ac59;
}

.text-danger {
	color: #ed5565;
}

.text-muted {
	color: #888888;
}

/* image */
.img-circle {
	border-radius: 50%;
}

.img-lg {
	width: 120px;
	height: 120px;
}

.avatar-upload-preview {
	position: absolute;
	top: 50%;
	transform: translate(50%, -50%);
	width: 200px;
	height: 200px;
	border-radius: 50%;
	box-shadow: 0 0 4px #ccc;
	overflow: hidden;
}

/* 拖拽列样式 */
.sortable-ghost{
	opacity: .8;
	color: #fff!important;
	background: #42b983!important;
}

/* 表格右侧工具栏样式 */
.top-right-btn {
	margin-left: auto;
}


.queryParamsForm {
	background:#FFF;
  margin-bottom: 10px;
	padding: 16px;
	&-handle, .btn-form {
		margin-left: auto;
	}
	.el-form--inline {
		display: flex;
    flex-wrap: wrap;
	}
}

.table-info {
	background: #FFFFFF;
	border-radius: 2px;
	padding: 16px;
}

.ui-tabs2table  {
	.el-tabs__item {
		padding: 0;
		min-width: 120px;
		box-sizing: border-box;
	}
	.el-tabs__nav-wrap:after {
		height: 1px;
	}
}

.el-drawer__body{
  border-top: 1px solid #E5E7F3;
  border-bottom: 1px solid #E5E7F3;
  padding: 24px !important;
}
.el-drawer__footer {
   padding: 12px 16px !important;
}
.el-drawer__header {
  padding: 16px 24px !important;
  margin-bottom: 0px !important;
  .el-drawer__close-btn {
    font-size: 18px;
  }
}
.el-drawer__title{
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #323233;
  line-height: 24px;
  text-align: left;
  font-style: normal;
}

.el-cascader {
  width: 100%;
}

 .el-message-box-icon--warning {
   color: #762ADB !important;
 }

 .el-message-box {
   padding: 0px !important;
 }
 .el-message-box__header{
   padding: 20px 30px;
   border-bottom: 1px solid #E5E7F3;
   .el-message-box__title{
     font-family: PingFangSC, PingFang SC;
     font-weight: 500;
     font-size: 18px;
     color: #151719;
     line-height: 24px;
     text-align: left;
     font-style: normal;
   }
   .el-message-box__close {
     font-size: 18px !important;
   }
 }
 .el-message-box__btns {
   padding: 20px 30px;
   border-top: 1px solid #E5E7F3;
 }
 .el-message-box__content {
   padding: 76px 30px;
   font-family: PingFangSC, PingFang SC;
   font-weight: 400;
   font-size: 14px;
   color: #52585F;
   line-height: 20px;
   text-align: left;
   font-style: normal;
 }
 .el-message-box__headerbtn {
   height: 64px !important;
 }



