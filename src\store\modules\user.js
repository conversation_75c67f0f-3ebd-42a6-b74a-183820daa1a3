import { login, logout, getInfo, queryUserAuthoritySidebarMenu } from '@/api/user'
import { getToken, setToken, removeToken } from '@/utils/auth'

const useUserStore = defineStore(
  'user',
  {
    state: () => ({
      token: getToken(),
      roles: [],
      permissions: [],

      userInfo: null,
      sidebarMenu: [],
    }),
    actions: {
      setAppToken(token) {
        setToken(token)
        this.token = token
      },
      // 登录
      login(userInfo) {
        return new Promise((resolve, reject) => {
          login(userInfo).then(res => {
            setToken(res.data.access_token)
            localStorage.setItem('updatePasswordFlag', res.data.loginResultVO.updatePasswordFlag)
            // localStorage.setItem('updatePasswordFlag',0)
            this.token = res.data.access_token
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      },
      // 获取用户信息
      getInfo() {
        return new Promise((resolve, reject) => {
          getInfo().then(res => {
            this.userInfo = res.data
            if(res?.data?.authorities === "*:*:*"){
              this.permissions = "*:*:*"
            }
            else{
              this.permissions = (res?.data?.authorities || []).map(item => item.authority)
            }
            resolve(res)
          }).catch(error => {
            console.log('error', error);
            reject(error)
          })
        })
      },
      // 获取路由菜单
      /* getSidebarMenu() {
        return new Promise((resolve, reject) => {
          queryUserAuthoritySidebarMenu().then(response => {
            const { data } = response
            this.sidebarMenu = data.platform.filter(item => item.visible === 1)
            console.log('this.sidebarMenu', this.sidebarMenu);

            resolve(data.platform)
          }).catch(error => {
            reject(error)
          })
        })
      }, */
      // 获取路由菜单
      getSidebarMenu() {
        return new Promise((resolve, reject) => {
          queryUserAuthoritySidebarMenu().then(response => {
            const { data } = response

            // 递归过滤可见的菜单项
            const filterVisibleMenu = (items) => {
              return items.filter(item => {
                if (item.visible !== 1) return false;
                if (item.children && item.children.length) {
                  item.children = filterVisibleMenu(item.children);
                }
                return true;
              });
            };

            this.sidebarMenu = filterVisibleMenu(data.platform);
            console.log('this.sidebarMenu', this.sidebarMenu);

            resolve(data.platform)
          }).catch(error => {
            reject(error)
          })
        })
      },

      resetToken() {
        this.token = ''
        this.roles = []
        this.permissions = []
        removeToken()
      },

      // 退出系统
      logOut() {
        return new Promise((resolve, reject) => {
          logout(this.token).then(() => {
            this.resetToken()
            removeToken()
            resolve()
          }).catch(error => {
            reject(error)
          })
        })
      }
    }
  })

export default useUserStore
