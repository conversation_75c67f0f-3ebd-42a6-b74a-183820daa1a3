<template>
  <div class="page">
    <div class="table-info">
      <div class="mb10">
        <el-button type="primary" v-hasPermi="['tenant:menu:add']" @click="handleOpenDialog('add', null)">
          新增菜单
        </el-button>
      </div>
      <el-tabs v-model="activeSystem" @tab-change="handleSystemChange">
        <!-- <el-tab-pane label="采购服务" name="pms" />
        <el-tab-pane label="销售服务" name="oms" />
        <el-tab-pane label="仓储服务" name="wms" />
        <el-tab-pane label="运输服务" name="tms" />
        <el-tab-pane label="供应商客户端" name="pms_app" />
        <el-tab-pane label="系统设置" name="supply" /> -->
        <el-tab-pane
      v-for="(item,index) in activeSystemList"
      :key="index"
      :label="item.name"
      :name="item.code"
    />
      </el-tabs>
      <el-table v-loading="loading" stripe :data="menuTableData" row-key="menuId" :tree-props="{ children: 'children' }"
        :cell-style="cellStyle">
        <template #empty>
          <Empty/>
        </template>
        <el-table-column label="菜单名称" min-width="200" show-overflow-tooltip>
          <template #default="scope">
            <template v-if="scope.row.icon && scope.row.icon.startsWith('el-icon')">
              <el-icon style="vertical-align: -0.15em">
                <component :is="scope.row.icon.replace('el-icon-', '')" />
              </el-icon>
            </template>
            <template v-else-if="scope.row.icon">
              <svg-icon :icon-class="scope.row.icon" />
            </template>
            <!--                        <template v-else>-->
            <!--                            <svg-icon icon-class="menu"/>-->
            <!--                        </template>-->
            {{ scope.row.menuName }}
          </template>
        </el-table-column>

        <el-table-column label="路由名称" prop="routeName" min-width="200" show-overflow-tooltip />

        <el-table-column label="路由路径" prop="path" show-overflow-tooltip min-width="200" />

        <el-table-column label="组件路径" prop="componentPath" show-overflow-tooltip min-width="200" />

        <el-table-column label="菜单标识" min-width="200" prop="menuCode" show-overflow-tooltip />
        <el-table-column label="菜单类型" prop="visible" min-width="100" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.visible ? "显性" : "隐形" }}
          </template>
        </el-table-column>
        <el-table-column label="排序" prop="priority" align="center" />
        <el-table-column label="状态" min-width="100" show-overflow-tooltip v-hasPermi="['tenant:menu:status']">
          <template #default="scope">
            <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" inline-prompt active-text="启用"
              inactive-text="禁用" @change="handleDisable(scope.row)" />
          </template>
        </el-table-column>

        <el-table-column fixed="right" label="操作" min-width="320">
          <template #default="scope">
            <el-button type="primary" link v-hasPermi="['tenant:menu:edit']" @click.stop="handleOpenDialog('update', scope.row)">
              编辑
            </el-button>
            <el-button v-if="scope.row.level !== 3" type="primary" link v-hasPermi="['tenant:menu:add:submenu']"
              @click="handleOpenDialog('addChildren', scope.row)">
              添加子菜单
            </el-button>
            <el-button v-if="scope.row.level == 2 || scope.row.level == 3" @click="goMenuAuthority(scope.row)"
                       v-hasPermi="['tenant:menu:authority']"
                       type="primary" link>
              功能权限
            </el-button>
            <el-button type="text" @click.stop="handleDelete(scope.row)" v-hasPermi="['tenant:menu:del']">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-drawer v-model="dialog.visible" :title="dialog.title" @close="handleCloseDialog" :close-on-click-modal="false"
      width="600">
      <el-form ref="menuFormRef" :model="menuForm" :rules="menuFormRules" label-width="112px" label-position="top">
        <el-form-item label="上级菜单" prop="parentName">
          <el-input v-model="menuForm.parentName" placeholder="请输入上级菜单" disabled clearable maxlength="100" />
        </el-form-item>
        <el-form-item label="菜单标识" prop="menuCode">
          <el-input v-model="menuForm.menuCode" placeholder="请输入菜单标识" clearable maxlength="100" />
        </el-form-item>
        <el-form-item label="菜单名称" prop="menuName">
          <el-input v-model="menuForm.menuName" placeholder="请输入菜单名称" clearable maxlength="100" />
        </el-form-item>
        <el-form-item label="路由名称" prop="routeName">
          <template #label>
            <span>
              路由名称
              <el-tooltip placement="bottom" effect="light">
                <template #content>
                  如果需要开启缓存，需保证页面 defineOptions 中的 name
                  与此处一致，建议使用驼峰。
                </template>
                <i-ep-QuestionFilled class="inline-block" />
              </el-tooltip>
            </span>
          </template>
          <el-input v-model="menuForm.routeName" placeholder="请输入路由名称" clearable maxlength="100" />
        </el-form-item>
        <el-form-item label="路由路径" prop="path">
          <template #label>
            <span>
              路由路径
              <el-tooltip placement="bottom" effect="light">
                <template #content>
                  定义应用中不同页面对应的 URL 路径，目录需以 /
                  开头，菜单项不用。例如：系统管理目录
                  /system，系统管理下的用户管理菜单 user。
                </template>
                <i-ep-QuestionFilled class="inline-block" />
              </el-tooltip>
            </span>
          </template>
          <el-input v-model="menuForm.path" placeholder="请输入路由路径" clearable maxlength="100" />
        </el-form-item>
        <el-form-item label="组件路径" prop="componentPath">
          <template #label>
            <span>
              组件路径
              <el-tooltip placement="bottom" effect="light">
                <template #content>
                  组件页面完整路径，相对于 src/views/，如
                  system/user/index，缺省后缀 .vue
                </template>
                <i-ep-QuestionFilled class="inline-block" />
              </el-tooltip>
            </span>
          </template>
          <!-- :disabled="menuForm.parentId=='0'" -->
          <el-input v-model="menuForm.componentPath" placeholder="请输入组件路径" clearable maxlength="100" />
        </el-form-item>
        <el-form-item label="图标" prop="icon" @mouseover="showClose" @mouseleave="hiddenClose">
          <icon-select v-model="menuForm.icon" :active-icon="menuForm.icon"/>
        <!--  <div v-if="showCloseIcon && menuForm.icon" class="icon-style" @click="closeIcon">
            <el-icon @click="closeIcon">
              <CircleClose />
            </el-icon>
          </div> -->
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input v-model="menuForm.priority" placeholder="请输入菜单展示顺序" type="number" clearable :min="0"
            maxlength="100" />
        </el-form-item>
        <el-form-item label="显性菜单" prop="visible">
          <el-radio-group v-model="menuForm.visible">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单描述" prop="menuDesc">
          <el-input v-model="menuForm.menuDesc" type="textarea" :rows="2" placeholder="请输入菜单描述" maxlength="100" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" :loading="loadingDialog">
            确定
          </el-button>
          <el-button @click="handleCloseDialog">取消</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessageBox, ElMessage } from 'element-plus'
const router = useRouter();

defineOptions({
  name: "menu",
  inheritAttrs: false,
});

import MenuAPI from "@/api/menu";

const menuFormRef = ref();
const loading = ref(false);
const loadingDialog = ref(false);
const dialog = reactive({
  title: "新增菜单",
  visible: false,
});
const menuTableData = ref();
let typeButton = ref();
let showCloseIcon = ref();
const activeSystemList = ref([])
let menuForm = ref({
  parentName: "",
  menuId: "",
  parentId: "",
  menuCode: "",
  menuName: "",
  routeName: "",
  path: "",
  componentPath: "",
  icon: "",
  priority: "",
  visible: 1,
  status: 1,
  menuDesc: "",
});
const menuFormRules = reactive({
  parentName: [
    { required: true, message: "请选择上级菜单", trigger: "change" },
  ],
  menuCode: [{ required: true, message: "请输入菜单标识", trigger: "blur" }],
  menuName: [{ required: true, message: "请输入菜单名称", trigger: "blur" }],
  routeName: [{ required: true, message: "请输入路由名称", trigger: "blur" }],
  path: [{ required: true, message: "请输入路由路径", trigger: "blur" }],
  componentPath: [
    { required: true, message: "请输入组件路由", trigger: "blur" },
  ],
});

const activeSystem = ref("pms");

function closeIcon() {
  menuForm.value.icon = "";
}

function showClose() {
  showCloseIcon.value = true;
}

function hiddenClose() {
  showCloseIcon.value = false;
}

function handleDisable(row) {
  let title: string = row.status == 1 ? "启用" : "禁用";
  ElMessageBox.confirm(`确定${title}吗？`, "提示", {
    type: "warning",
  })
    .then(() => {
      const params = {
        status: row.status,
        menuId: row.menuId,
      };
      MenuAPI.updateEnableStatus(params).then((res) => {
        ElMessage.success(`${title}成功`);
        handleQuery();
      });
    })
    .catch(() => {
      row.status = row.status === 1 ? 0 : 1;
    });
}

function handleQuery() {
  loading.value = true;
  MenuAPI.getOfficialMenuTreeList({ systemType: activeSystem.value })
    .then((res) => {
      menuTableData.value = res.data;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleResetQuery() {
  handleQuery();
}

function goMenuAuthority(row) {
  router.push({
    path: "/organization/tenantMenuAuthority",
    query: {
      menuId: row.menuId,
      menuName: row.menuName,
      systemType: activeSystem.value,
    },
  });
}

/**打开表单弹窗*/
function handleOpenDialog(type, row) {
  typeButton.value = type;
  if (typeButton.value == "update") {
    MenuAPI.getMenuDetail(row.menuId).then((res) => {
      menuForm.value = { ...res.data };
      menuForm.value.parentId = row.parentId;
      menuForm.value.parentName = row.menuName;
      dialog.title = "编辑菜单";
      dialog.visible = true;
    });
  } else {
    if (typeButton.value == "add") {
      dialog.title = "新增菜单";
      menuForm.value.parentId = 0;
      menuForm.value.parentName = "无";
      menuForm.value.componentPath = "Layout";
      // menuForm.value.componentPath = ""
      dialog.visible = true;
    } else {
      dialog.title = "添加子菜单";
      menuForm.value.parentId = row.menuId;
      menuForm.value.parentName = row.menuName;
      dialog.visible = true;
    }
  }
}

/** 菜单保存提交 */
function submitForm() {
  menuFormRef.value.validate((isValid: boolean) => {
    if (isValid) {
      let params = {
        systemType: activeSystem.value,
        menuId: menuForm.value.menuId,
        parentId: menuForm.value.parentId,
        menuCode: menuForm.value.menuCode,
        menuName: menuForm.value.menuName,
        routeName: menuForm.value.routeName,
        path: menuForm.value.path,
        componentPath: menuForm.value.componentPath,
        icon: menuForm.value.icon,
        priority: menuForm.value.priority,
        visible: menuForm.value.visible,
        status: menuForm.value.status,
        menuDesc: menuForm.value.menuDesc,
      };
      loadingDialog.value = true;
      const msg =
        typeButton.value == "update"
          ? "编辑成功"
          : typeButton.value == "add"
            ? "新增成功"
            : "添加子菜单成功";
      const url =
        typeButton.value == "update" ? MenuAPI.menuUpdate : MenuAPI.menuAdd;
      if (
        loadingDialog.value == "add" ||
        loadingDialog.value == "addChildren"
      ) {
        delete params.menuId;
      }
      url(params)
        .then(() => {
          ElMessage.success(msg);
          handleCloseDialog();
          handleQuery();
        })
        .finally(() => (loadingDialog.value = false));
    }
  });
}

/** 删除菜单 */
function handleDelete(row) {
  ElMessageBox.confirm("确认删除?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(
    function () {
      loading.value = true;
      let data = {
        menuId: row.menuId,
      };
      MenuAPI.menuDelete(data)
        .then(() => {
          ElMessage.success("删除成功");
          handleQuery();
        })
        .finally(() => (loading.value = false));
    },
    function () {
      ElMessage.info("已取消");
    }
  );
}

/** 关闭弹窗 */
function handleCloseDialog() {
  menuForm.value.parentName = "";
  menuForm.value.menuId = "";
  menuForm.value.parentId = "";
  menuForm.value.menuCode = "";
  menuForm.value.menuName = "";
  menuForm.value.routeName = "";
  menuForm.value.path = "";
  menuForm.value.componentPath = "";
  menuForm.value.icon = "";
  menuForm.value.priority = "";
  (menuForm.value.visible = 1),
    (menuForm.value.status = 1),
    (menuForm.value.menuDesc = "");
  menuFormRef.value.clearValidate();
  dialog.visible = false;
}

const cellStyle = (data) => {
  if (data.row.level == 3) {
    return {
      borderBottom: "0px",
      background: "#FBFBFB",
    };
  }
};

function handleSystemChange() {
  handleQuery();
}

function getTenantMenuSystemTypeList() {
  MenuAPI.queryTenantMenuSystemTypeList()
        .then((res) => {
          activeSystemList.value = res.data
        })
        .finally(() => {});
}

onMounted(() => {
  getTenantMenuSystemTypeList()
  handleQuery();
});
</script>
<style lang="scss" scoped>
.icon-style {
  position: absolute;
  top: 2px;
  right: 30px;
}
</style>
<style lang="scss">
.el-table__expand-icon>.el-icon {
  width: 0 !important;
  height: 0 !important;
  border-left: 5px solid transparent !important;
  border-right: 5px solid transparent !important;
  border-top: 5px solid #7b88a8 !important;
  transform: rotate(-90deg) !important;
}
</style>
