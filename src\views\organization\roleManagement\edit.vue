<template>
  <el-drawer :value="isVisible" :title="title" :close-on-click-modal="false" width="600px" @close="close">
    <el-form ref="$elForm" :model="form" :rules="rules" label-position="top">
      <el-form-item :label="$t('roleManagement.label.roleName')" prop="roleName">
        <el-input v-model.trim="form.roleName"  maxlength="20" clearable  :placeholder="$t('roleManagement.prompt.inputTips')"
                  :disabled="editType === 'permission'"/>
      </el-form-item>

      <el-form-item :label="$t('roleManagement.label.status')" prop="status">
        <el-radio-group  v-model="form.status">
          <el-radio :label="1">{{ $t('roleManagement.operation.enable') }}</el-radio>
          <el-radio :label="0">{{ $t('roleManagement.operation.disable') }}</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item :label="$t('roleManagement.label.roleDesc')" prop="roleDesc">
        <el-input v-model.trim="form.roleDesc" maxlength="50"  show-word-limit type="textarea" :rows="4" :placeholder="$t('roleManagement.prompt.inputTips')" 
                  :disabled="editType === 'permission'"/>
      </el-form-item>

      <el-form-item :label="$t('roleManagement.label.permission')" prop="menuIds" class="permission-label">
        <el-tree
            class="tree-menu"
            ref="treeRef"
            :data="menuData"
            show-checkbox
            node-key="authorityId"
            default-expand-all
            highlight-current
            :props="defaultProps"
            :default-checked-keys="defaultCheckedKeys"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div>
        <el-button @click="close">{{ $t('common.cancel') }}</el-button>
        <el-button type="primary" :loading="submitLoading" @click="onClickConfirm">{{ $t('common.confirm') }}
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup name="roleManagement">
import {addRole, updateRole, roleMenuList, allMenuList, grantRoleMenu} from "@/api/organization.js";
import {ElMessage, ElTree} from "element-plus";

const {proxy} = getCurrentInstance()

const emit = defineEmits()

defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
})

let data = reactive({
  form: initForm(),
  rules: {
    roleName: [
      {required: true, message: proxy.$t('roleManagement.placeholder.roleName'), trigger: 'blur'},
      {pattern: /^[\u4e00-\u9fa5a-zA-Z0-9]+$/, message: proxy.$t('roleManagement.placeholder.roleNameFomart'), trigger: ['change', 'blur']}
    ],
    status:[
      {required: true, message: proxy.$t('roleManagement.placeholder.status'), trigger: 'blur'},
    ]
  },
  submitLoading: false,
  menuData: [],
  editType: ''
})

let {form, rules, submitLoading, menuData, editType} = toRefs(data);

const defaultCheckedKeys = ref([])
const menuIds = ref([])
const defaultProps = {label: 'authorityName', children: 'children'}

function initForm() {
  return {
    roleId: '',
    roleName: '',
    status: 1,
    roleDesc: '',
  }
}

function setEditType(data) {
  editType.value = data
}

function setFormData(data) {
  form.value = {...data}
}

let $elForm = ref()

function onClickConfirm() {
  if (editType.value === 'add') {
    submitAddRole()
  } else if (editType.value === 'edit') {
    submitUpdateRole()
  } 
  // else {
  //   handleGrantRoleMenu()
  // }
}

function submitAddRole() {
  $elForm.value.validate(valid => {
    if (!valid) return
    submitLoading.value = true
    menuIds.value = getCheckedKeys()
    let roleSysList = [{
       authorityIds: menuIds.value,
       platformType: "platform"
    }]
    let params = {...form.value, ...{roleSysList: roleSysList}, platformType: "platform"}
    addRole(params).then(res => {
      emit('submitted')
      isVisible.value = false
      return res
    }).finally(() => {
      submitLoading.value = false
    })
  })
}

function submitUpdateRole() {
  $elForm.value.validate(valid => {
    if (!valid) return
    submitLoading.value = true
    menuIds.value = getCheckedKeys()
    let roleSysList = [{
       authorityIds: menuIds.value,
       platformType: "platform"
    }]
    let params = {...form.value, ...{roleSysList: roleSysList},  platformType: "platform"}
    updateRole(params).then(res => {
      emit('submitted')
      isVisible.value = false
      return res
    }).finally(() => {
      submitLoading.value = false
    })
  })
}

// function handleGrantRoleMenu() {
//   submitLoading.value = true
//   menuIds.value = getCheckedKeys()
//   let checkedIds = []
//   menuData.value.forEach(item => {
//     let flag = false
//     item.subMenuList.forEach(v =>{
//       if (menuIds.value.includes(v.menuId)) {
//         flag = true
//       }
//     })
//     if(flag && !menuIds.value.includes(item.menuId)){
//       checkedIds.push(item.menuId) // 父级id 传入--接口传参需要
//     }
//   })
//   let params = {
//     roleId: form.value.roleId,
//     menuIds: [...menuIds.value, ...checkedIds]
//   }
//   grantRoleMenu(params).then(res => {
//     emit('submitted')
//     isVisible.value = false
//     return res
//   }).finally(() => {
//     submitLoading.value = false
//   })
// }

function close() {
  isVisible.value = false
  reset()
}

function reset() {
  $elForm.value.clearValidate()
  $elForm.value.resetFields()
  form.value = initForm()
  menuData.value = []
  defaultCheckedKeys.value = []
  menuIds.value = []
}

let dialogVisible = ref(false)
let isVisible = computed({
  get() {
    return dialogVisible.value
  },
  set(value) {
    emit('update:modelValue', value)
  }
})

function queryAllMenuList() {
  allMenuList({}).then(res => {
    if (res.code === 0) {
    let arrData = res.data[0].roleAuthorityInfo.treeList
      menuData.value = removeNullValues(arrData);
    }
  })
}


function removeNullValues(arr) {
  return arr.reduce((acc, item) => {
    // 如果项是一个对象且含有children属性，递归处理
    if (item && typeof item === 'object' && item.children) {
      const filteredChildren = removeNullValues(item.children);
      if (filteredChildren.length > 0) {
        acc.push({ ...item, children: filteredChildren });
      }
    } else if (item.authorityId != null) { // 如果是非null值，直接添加到结果数组
      acc.push(item);
    }
    return acc;
  }, []);
}

function queryRoleMenuList() {
  allMenuList({roleId: form.value.roleId}).then(res => {
    if (res.code === 0) {
      menuData.value =res.data[0].roleAuthorityInfo.treeList
      defaultCheckedKeys.value = res.data[0].roleAuthorityInfo.authorityIds
    }
  })
}

const treeRef = ref()
const getCheckedKeys = () => {
   const fullCheckedKeys =  treeRef.value?.getCheckedKeys(false)
  const halfCheckedKeys = treeRef.value?.getHalfCheckedKeys(false)
  const checked = [...fullCheckedKeys,...halfCheckedKeys]
  return checked
}

defineExpose({
  setFormData,
  setEditType,
  queryAllMenuList,
  queryRoleMenuList
})
</script>
<style lang="scss" scoped>
.tree-menu {
  border: 1px solid #D7DBDF;
  border-radius: 4px;
  padding: 8px 11px;
  width: 100%;
  height: 360px;
  overflow: auto;
}

.permission-label {
  position: relative;

  // &:before {
  //   position: absolute;
  //   color: var(--el-color-danger);
  //   content: "*";
  //   left: -8px;
  //   top: 8px;
  // }
}

</style>
