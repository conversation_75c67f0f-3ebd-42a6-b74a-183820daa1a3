import { createI18n, useI18n } from "vue-i18n";
// 本地语言包
import zhCnLocale from "./package/zh-CN";
import enLocale from "./package/en";
import koLocale from "./package/ko";
import viLocale from "./package/vi";
import idLocale from "./package/id";
import ruLocale from "./package/ru";

const messages = {
  "zh-CN": {
    ...zhCnLocale,
  },
  en: {
    ...enLocale,
  },
  // 韩国
  ko: {
    ...koLocale,
  },
  // 越南语
  vi: {
    ...viLocale,
  },
  // 印度尼西亚语（id）
  id: {
    ...idLocale,
  },
  // 俄语（ru）
  ru: {
    ...ruLocale,
  }
};

const i18n = createI18n({
  legacy: false,
  locale: 'zh-CN',
  messages: messages,
  globalInjection: true,
});

export default i18n;
