<template>
  <div class="doublePage">
    <div class="doubleArea doubleAreaMain">
      <div class="queryParamsForm">
        <el-form :inline="true" :model="mainParams">
          <el-form-item label="采购单号">
            <el-input v-model.trim="mainParams.purchaseOrderCode" placeholder="请输入" @keyup.enter.native="handleMainQuery" clearable>
            </el-input>
          </el-form-item>
          <el-form-item class="queryParamsForm-handle">
            <el-button v-resubmit @click="resetMainParams">{{ $t('common.reset') }}</el-button>
            <el-button v-resubmit type="primary" @click="handleMainQuery">{{ $t('common.search') }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="doubleArea-bd">
        <div class="doubleArea-title" style="margin-bottom: 10px;">待装箱发货</div>
        <el-table :data="mainTable" v-loading="mainTableLoading" class="doubleArea-table" border show-overflow-tooltip>
          <el-table-column prop="date" label="Date" width="180" />
          <el-table-column prop="name" label="Name" width="180" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
          <el-table-column prop="address" label="Address" />
        </el-table>
      </div>
      <div class="doubleArea-ft">
        <pagination v-show="mainTableTotal > 0" :total="mainTableTotal" v-model:page="mainParams.page"
          v-model:limit="mainParams.limit" @pagination="getMainTable" />
      </div>
    </div>
    <div class="doubleArea doubleAreaSecondary">
      <div class="queryParamsForm">
        <el-form :inline="true" :model="subParams">
          <SelectSupplier v-model="subParams.supplierId"></SelectSupplier>
          <el-form-item label="原材料">
            <el-input v-model.trim="subParams.purchaseOrderCode" placeholder="请输入" @keyup.enter.native="handleSubQuery"
              clearable>
            </el-input>
          </el-form-item>
          <el-form-item class="queryParamsForm-handle">
            <el-button v-resubmit @click="resetSubParams">{{ $t('common.reset') }}</el-button>
            <el-button v-resubmit type="primary" @click="handleSubQuery">{{ $t('common.search') }}</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="doubleArea-bd">
        <div class="doubleArea-tbHeader">
          <div class="doubleArea-title">待装箱发货</div>
          <el-button type="primary" plain v-resubmit>装箱发货</el-button>
        </div>

        <el-table :data="subTable" v-loading="subTableLoading" class="doubleArea-table" border show-overflow-tooltip>
          <el-table-column prop="date" label="Date" width="180" />
          <el-table-column prop="name" label="Name" width="180" />
          <el-table-column prop="address" label="Address" />
        </el-table>
      </div>
      <div class="doubleArea-ft">
        <pagination v-show="subTableTotal > 0" :total="subTableTotal" v-model:page="subParams.page"
          v-model:limit="subParams.limit" @pagination="getSubTable" />
      </div>
    </div>
  </div>
</template>
<style lang="scss" scoped>

</style>

<script setup>
import SelectSupplier from "@/components/FormColumn/SelectSupplier";
import { useMainData, useSubData } from './hooks'
let { mainTable, mainTableTotal, mainTableLoading, mainParams, getMainTable, handleMainQuery, resetMainParams } = useMainData()
let { subTable, subTableTotal, subTableLoading, subParams, getSubTable, handleSubQuery, resetSubParams } = useSubData()

</script>
