<template>
  <el-drawer v-model="isVisible" :title="title" :close-on-click-modal="false" width="500px" @close="close">
    <el-form :model="deptForm" :rules="rules" ref="deptRef" label-position="top">
      <el-form-item label="部门名称" prop="deptName">
        <el-input type="text" placeholder="请输入" v-model="deptForm.deptName" :maxlength="50" clearable></el-input>
      </el-form-item>
      <el-form-item label="部门层级" prop="deptLevel">
        <el-radio-group v-model="deptForm.deptLevel" :disabled="deptLevelStatus">
          <el-radio :label="1">一级部门</el-radio>
          <el-radio :label="2">二级部门</el-radio>
          <el-radio :label="3">三级部门</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="上级部门" prop="parentId" v-if="deptForm.deptLevel !== 1">
        <el-tree-select
          v-model="deptForm.parentId"
          :data="processedDeptTree"
          placeholder="请选择"
          clearable
          check-strictly
          :disabled="deptTreeStatus"
          :props="{
            label: 'deptName',
            value: 'id',
            children: 'children',
            disabled: 'disabled'
          }"
        />
      </el-form-item>
      <el-form-item label="部门描述" prop="deptDesc">
        <el-input
          type="textarea"
          placeholder="请输入"
          v-model="deptForm.deptDesc"
          :maxlength="50"
          :rows="4"
          show-word-limit>
        </el-input>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="submitForm">确定</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import { addDept, updateDept, getDeptTree } from '@/api/dept.js'
import { ElMessage } from 'element-plus'
import { ref, computed, watch, nextTick } from 'vue'

const emit = defineEmits(['update:modelValue', 'submitted'])
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  }
})

const data = reactive({
  deptForm: initForm(),
  rules: {
    deptName: [
      { required: true, message: '请输入部门名称', trigger: 'blur' },
      { pattern: /^[^\s]{1,20}$/, message: '部门名称只能包含字母、数字、下划线和中文,且长度在1~20之间', trigger: ['blur', 'change'] }
    ],
    deptLevel: [
      { required: true, message: '请选择部门层级', trigger: 'change' }
    ],
    parentId: [
      { required: true, message: '请选择上级部门', trigger: 'change' }
    ]
  },
  submitLoading: false,
  editType: 'add',
  parentDeptOptions: [],
  processedDeptTree: [],
  deptLevelStatus: false,  // 部门层级状态
  deptTreeStatus: false
})

const { deptForm, rules, submitLoading, editType, parentDeptOptions, processedDeptTree, deptLevelStatus, deptTreeStatus } = toRefs(data)
const deptRef = ref()

const isVisible = computed({
  get: () => props.modelValue,
  set: (val) => emit('update:modelValue', val)
})

function setDeptLevelStatus(status) {
  deptLevelStatus.value = status
}

function setDeptTreeStatus(status) {
  deptTreeStatus.value = status
}
function initForm() {
  return {
    deptName: '',
    deptLevel: 1, // 默认一级部门
    parentId: 0,
    deptDesc: '',
    id: ''
  }
}

// 处理部门树数据，根据选择的层级设置可选状态
const processDeptTree = (depts, targetLevel) => {
  if (!depts) return []
  
  return depts.map(dept => {
    const processed = {
      ...dept,
      disabled: dept.deptLevel !== targetLevel
    }
    
    if (dept.children && dept.children.length > 0) {
      processed.children = processDeptTree(dept.children, targetLevel)
    }
    
    return processed
  })
}

// 监听部门层级变化，处理部门树显示
watch(() => deptForm.value.deptLevel, (newLevel) => {
  console.log('newLevel--------', newLevel)
  if (!newLevel) return
  
  // 重置上级部门选择
  deptForm.value.parentId = undefined
  
  // 根据选择的部门层级处理可选的上级部门
  if (newLevel === 2) {
    // 如果选择二级部门，显示一级和二级部门，但只有一级部门可选
    processedDeptTree.value = processDeptTree(parentDeptOptions.value, 1)
  } else if (newLevel === 3) {
    // 如果选择三级部门，显示所有层级部门，但只有二级部门可选
    processedDeptTree.value = processDeptTree(parentDeptOptions.value, 2)
  } else {
    processedDeptTree.value = []
  }
}, { immediate: true })

// 获取部门树数据
const getParentDepts = async () => {
  try {
    const { code, data } = await getDeptTree()
    if (code === 0) {
      parentDeptOptions.value = data || []
      // 初始化部门树
      if (deptForm.value.deptLevel) {
        const targetLevel = deptForm.value.deptLevel === 2 ? 1 : 2
        processedDeptTree.value = processDeptTree(parentDeptOptions.value, targetLevel)
      }
    }
  } catch (error) {
    console.error('获取部门树失败:', error)
  }
}

function close() {
  isVisible.value = false
  reset()
}

function reset() {
  deptRef.value?.clearValidate()
  deptRef.value?.resetFields()
  deptForm.value = initForm()
}

function submitForm() {
  deptRef.value?.validate(valid => {
    if (!valid) return
    submitLoading.value = true
    const params = {
      deptName: deptForm.value.deptName,
      deptLevel: deptForm.value.deptLevel,
      parentId: deptForm.value.deptLevel === 1 ? 0 : deptForm.value.parentId,
      deptDesc: deptForm.value.deptDesc
    }

    const request = editType.value === 'add' ? addDept : updateDept
    if (editType.value === 'edit') {
      params.id = deptForm.value.id
    }

    request(params).then(res => {
      if (res.code === 0) {
        ElMessage.success(editType.value === 'add' ? '新增成功' : '编辑成功')
        emit('submitted')
        close()
      }
    }).finally(() => {
      submitLoading.value = false
    })
  })
}

function setEditType(type) {
  editType.value = type
  if (type === 'add') {
    getParentDepts()
  }
}


function setFormData(data) { // 部门新增设置表单
  deptForm.value = {
    id: data.id,
    deptName: data.deptName,
    deptLevel: data.deptLevel,
    parentId: data.parentId,
    deptDesc: data.deptDesc
  }
  if (data.deptLevel !== 1) {
    getParentDepts()
  }
}

/* function setFormDataEdit(status) { // 部门编辑设置表单
  deptForm.value = {
    id: data.id,
    deptName: data.deptName,
    deptLevel: data.deptLevel,
    parentId: data.parentId ,
    deptDesc: data.deptDesc
  }

} */

function setParentDept(data, type ='add') {
  if (data) {
    // 先设置部门层级，这样watch会先处理树数据
    deptForm.value.deptLevel = data.deptLevel + 1
    
    // 使用nextTick确保树数据处理完成后再设置parentId
    if(type === 'add'){
      nextTick(() => {
        deptForm.value.parentId = data.id
        // 确保树数据已经处理完成后，重新触发一次处理
        const targetLevel = deptForm.value.deptLevel === 2 ? 1 : 2
        processedDeptTree.value = processDeptTree(parentDeptOptions.value, targetLevel)
      })

    }
    else if(type === 'edit'){
      nextTick(() => {
        deptForm.value.parentId = data.parentId
        // 确保树数据已经处理完成后，重新触发一次处理
        const targetLevel = deptForm.value.deptLevel === 2 ? 1 : 2
        processedDeptTree.value = processDeptTree(parentDeptOptions.value, targetLevel)
    })
    }
    
  }
}

defineExpose({
  setFormData,
  setEditType,
  setParentDept,
  setDeptLevelStatus,
  setDeptTreeStatus,
  /* setFormDataEdit */
})
</script>

<style scoped lang="scss">
.dialog-footer {
  padding: 20px 0;
  text-align: right;
}
</style>
