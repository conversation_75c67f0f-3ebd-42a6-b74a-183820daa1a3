import OSS from 'ali-oss';
import { getOssStsToken, previewFile } from '@/api/file.js';
// import { Message } from 'element-plus';

let credentials = null; // STS凭证
let ossClient = null; // oss客户端实例
let folder = ''; // 文件夹
let expiration = ''; // 过期时间
let bucket = ''; // bucket名称
let OPCODE = '';
const region = 'oss-cn-shanghai'; // oss服务区域名称

// 获取STS Token
function getCredential (optCode) {
  const params = {
    opCode: optCode
  };
  return getOssStsToken(params).then(res => {
    if (res.code === 0) {
      credentials = res.data;
      folder = res.data.bucketName;
      bucket = res.data.bucketName;
      expiration = res.data.expirationTime;
      initOSSClient();
    } else {
      // Message.error(res.message);
    }
  }).catch(err => {
    // Message.error(err);
  });
}

// 创建OSS Client
function initOSSClient () {
  const { accessKeyId, secretAccessKey, securityToken } = credentials;
  ossClient = new OSS({
    accessKeyId: accessKeyId,
    accessKeySecret: secretAccessKey,
    stsToken: securityToken,
    secure: true,
    bucket,
    region
  });
}

// 创建uuid随机数
function uuid () {
  let temp_url = URL.createObjectURL(new Blob());
  let uuid = temp_url.toString();
  URL.revokeObjectURL(temp_url);
  return uuid.substring(uuid.lastIndexOf('/') + 1);
}

// 普通上传
/*
  type为folder的子文件夹，主要为了便于设置不同文件类型的失效时间，取值为image, apk, doc, excel
  操作码opCode,取值范围【default,public-read】;default代表私密，public-read代表公共访问；不传取default
*/
export async function commonUpload (file, type = 'image', opCode = 'default') {
  if (!ossClient || OPCODE !== opCode) {
    await getCredential(opCode);
  }
  OPCODE = opCode;
  const nowTime = new Date().getTime();
  const expirationTime = expiration * 1000;
  if (nowTime > expirationTime) {
    ossClient && ossClient.cancel();
    await getCredential(opCode);
  }
  const extensionName = file.name.substring(file.name.lastIndexOf('.'));
  const fileName = uuid() + extensionName;
  const response = await ossClient.put(`/${folder}/${type}/${fileName}`, file);

  if(opCode === 'default'){ // 默认私密
    // originalFileName: file.name  原文件名
     response.url = JSON.stringify({ bucket: folder, fileName: response.name, originalFileName: file.name });
    //  response.bucket = folder;
  }
  else if(opCode === 'public-read'){ // 公共访问 ---response.url就是文件的访问地址，可以直接使用
    response.url = JSON.stringify({ bucket: folder, fileName: response.url, originalFileName: file.name });
  }
  return response;
}

// 预览图片:如果图片name带有http或者https，则认为是公共链接，直接展示，不需要调用预览接口
// [{ bucket: folder, fileName: response.name }]
// fileName:上传oss后的文件名
// originalFileName:上传前的文件名
const customUrlCode = import.meta.env.VITE_APP_CUSTOM_URL_CODE;
export async function previewSingle (bucket, fileName, originalFileName) {
  if (isHttp(fileName)) {
    return {bucket, name: originalFileName, url: fileName};
  } else {
    const res = await previewFile([{ bucket, fileName, customUrlCode}]);
    /*  {
          "code": 0,
          "message": "操作成功",
          "path": null,
          "data": {
              "urls": [
                  {
                      "objectId": null,
                      "bucket": "yt-oxms-uat",
                      "fileName": "yt-oxms-uat/image/5444bf94-13f9-40a7-90e9-15c23be080e2.png",
                      "url": "https://yt-oxms-uat.oss-cn-shanghai.aliyuncs.com/yt-oxms-uat/image/5444bf94-13f9-40a7-90e9-15c23be080e2.png?Expires=1738999053&OSSAccessKeyId=LTAI5tBYMyYUtrFwNCDcJ2FT&Signature=EZR8ozddgnUTqeSaB%2Bfc5zQpHfA%3D"
                  }
              ]
          },
            "extra": null,
            "timestamp": "1738997853061"
        } */
    if(res?.code === 0){
      // return {bucket, name: originalFileName, url: decodeURIComponent(res?.data?.urls[0].url)};
      return {bucket, name: originalFileName, url: res?.data?.urls[0].url};
    }
    return null;
  }
}

// 暂时没有用到，后续有需要再补充
export async function previewMuiltiple (bucket, fileNames) {}
// 判断是否是http或者https
function isHttp (url) {
  return url?.indexOf('http') === 0;
}
// export { commonUpload, previewSingle, previewMuiltiple };
export default commonUpload;
