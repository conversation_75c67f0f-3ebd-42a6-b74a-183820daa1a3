<!--<template>-->
<!--  <div class="image-viewer-wrapper">-->
<!--    <el-image-viewer-->
<!--      v-if="visible"-->
<!--      :url-list="urlList"-->
<!--      :initial-index="index"-->
<!--      @close="close"-->
<!--    />-->
<!--  </div>-->
<!--</template>-->

<!--<script lang="ts" setup>-->
<!--import { useVModel } from '@vueuse/core';-->

<!--const props = defineProps({-->
<!--  modelValue: {-->
<!--    type: Boolean,-->
<!--    default: false-->
<!--  },-->
<!--  urlList: {-->
<!--    type: Array as any,-->
<!--    default: () => []-->
<!--  },-->
<!--  index: {-->
<!--    type: Number,-->
<!--    default: () => 0-->
<!--  }-->
<!--})-->

<!--const emit = defineEmits(['update:modelValue'])-->
<!--const visible = useVModel(props, 'modelValue', emit)-->


<!--function close() {-->
<!--  visible.value = false-->
<!--}-->
<!--</script>-->

<!--<style scoped lang="scss"></style>-->
