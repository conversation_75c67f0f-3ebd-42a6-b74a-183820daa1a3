<template>
  <el-dialog width="500px" v-model="isVisible" :title="title" :close-on-press-escape="false" :close-on-click-modal="false" :show-close="showCloseButton" class="updatePassword">
    <el-form ref="updateFormRef" :model="form" :rules="data.rules" label-width="80px">
      <el-form-item :label="$t('changePassword.label.oldPassword')" prop="oldPassword">
        <el-input type="password" v-model="form.oldPassword" :placeholder="$t('common.placeholder.inputTips')" clearable show-password style="width: 50%"></el-input>
      </el-form-item>
      <el-form-item :label="$t('changePassword.label.newPassword')" prop="newPassword">
        <el-input type="password" v-model="form.newPassword" :placeholder="$t('common.placeholder.inputTips')" minlength="8" maxlength="20" show-password clearable style="width: 50%"></el-input>
      </el-form-item>
      <el-form-item :label="$t('changePassword.label.confirmPassword')" prop="confirmPassword">
        <el-input type="password" v-model="form.confirmPassword" :placeholder="$t('common.placeholder.inputTips')" minlength="8" maxlength="20" show-password clearable style="width: 50%"></el-input>
      </el-form-item>
    </el-form>
    <template #footer>
<!--      <el-button @click="isVisible = false">{{ $t('common.cancel') }}</el-button>-->
      <el-button :loading="data.submitLoading" v-resubmit type="primary" @click="onClickConfirm">{{ $t('common.confirm') }}</el-button>
    </template>
  </el-dialog>
</template>
<script setup>
import { updatePassword } from "@/api/user";
import { computed, getCurrentInstance, nextTick, onMounted, reactive } from 'vue'
import {ElMessage} from "element-plus";
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

// const { proxy: $vm } = getCurrentInstance()
const { proxy} = getCurrentInstance()

let updateFormRef = ref()
const props = defineProps({
	dialogVisible: {
		type: Boolean,
		default: false
	},
	title: {
		type: String,
		default: ''
	}
})

const showCloseButton = localStorage.getItem('updatePasswordFlag')==1?false:true
const data = reactive({
  dialogVisible: false,
	form: {
		oldPassword: undefined,       //	旧密码
		newPassword: undefined,          //	新密码
		confirmPassword: undefined,   //	确认新密码
	},
	rules: {
		oldPassword: [
			{ required: true, message: proxy.$t('changePassword.rules.oldPassword'), trigger: 'change' },
		],
		newPassword: [
			{ required: true, message:  proxy.$t('changePassword.rules.newPassword'), trigger: 'change' },
            { min: 8, max: 20, message: "长度在8到20个字符", trigger: "change" },
			{ pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Za-z])(?=\S*[!@$%^&_=.])\S*$/, message: proxy.$t('changePassword.rules.passwordFomart'), trigger: 'change' },
		],
		confirmPassword: [
			{ required: true, message: proxy.$t('changePassword.rules.confirmPassword'), trigger: 'change' },
            { min: 8, max: 20, message: "长度在8到20个字符", trigger: "change" },
            { pattern: /^\S*(?=\S{6,})(?=\S*\d)(?=\S*[A-Za-z])(?=\S*[!@$%^&_=.])\S*$/, message: proxy.$t('changePassword.rules.passwordFomart'), trigger: 'change' },
			{ required: true, validator: validateConfirmPassword, trigger: 'change' }
		],
	},
	submitLoading: false,
})

let { form } = toRefs(data)

const isVisible = computed({
	get: () => {
		return data.dialogVisible
	},
	set: (value) => {
    data.dialogVisible = value
		close()
		reset()
	}
})

function validateConfirmPassword(rule, value, callback) {
  if (value !== form.value.newPassword) {
    callback(new Error(proxy.$t('changePassword.message.passwordNoSameTips')));
  } else {
    callback();
  }
}

function reset() {
  console.log(11111);

	form.value = initForm()
	nextTick(() => {
		updateFormRef.value.clearValidate()
		updateFormRef.value.resetFields()
	})
}

function initForm() {
	return {
		oldPassword: undefined, //	旧密码
		password: undefined, //	新密码
		confirmPassword: undefined, //	确认新密码
	}
}

function onClickConfirm() {
	updateFormRef.value.validate(valid => {
		if (!valid) return
		data.submitLoading = true
		updatePassword(data.form).then(res => {
            ElMessage.success(proxy.$t('changePassword.message.updateSucess'))
			isVisible.value = false
            localStorage.removeItem('updatePasswordFlag')
            userStore.logOut().then(() => {
                location.href = '/index';
            })
		}).finally(() => {
			data.submitLoading = false
		})
	})
}

function open(val) {
  // data.dialogVisible = true
  data.dialogVisible = !val?localStorage.getItem('updatePasswordFlag')==1?true:false:true
}

defineExpose({
  open,
})
</script>
<style lang="scss" >
</style>
<style lang="scss" >
    .updatePassword{
        .el-dialog__body{
            display: flex;
            justify-content: center;
        }
        .el-input {
            width: 280px !important;
        }
    }

</style>
