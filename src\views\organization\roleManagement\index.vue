<template>
  <div class="page roleManagement">
    <div class="queryParamsForm">
      <!-- 搜索框 -->
      <el-form :inline="true" v-model='searchForm' ref='searchRef'>
        <!-- 角色名称 -->
        <el-form-item prop='roleName' :label="$t('roleManagement.label.roleName')">
          <el-input type="text" :placeholder="$t('roleManagement.prompt.inputTips')" v-model.trim="searchForm.roleName"
                    clearable>
          </el-input>
        </el-form-item>
        <!-- 角色状态 -->
        <el-form-item prop='status' :label="$t('roleManagement.label.roleStatus')">
          <el-select v-model="searchForm.status" clearable :placeholder="$t('roleManagement.prompt.selectTips')">
            <el-option v-for="(item,index) in roleStatusList" :key="index" :label="item.statusName"
                       :value="item.statusId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="queryParamsForm-handle">
          <el-button type="primary" @click="searchEvent" v-hasPerm="['sys:role:search']">{{ $t('common.search') }}</el-button>
          <el-button @click="resetEvent" v-hasPerm="['sys:role:reset']">{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-info">
      <div class="mb10">
        <el-button @click="openAdd" type="primary" v-hasPerm="['sys:role:add']">{{ $t('roleManagement.button.addRole') }}</el-button>
      </div>
      <el-table v-loading="tableLoading" :data="tableData" stripe  style="width: 100%">
        <el-table-column prop="roleName" :label="$t('roleManagement.label.roleName')" />
        <el-table-column prop="userCount" :label="$t('roleManagement.label.accountNum')" />
        <el-table-column prop="roleDesc" :label="$t('roleManagement.label.roleDesc')" />
        <el-table-column :label="$t('roleManagement.label.status')">
          <template #default="scope">
            <el-switch  :active-text="$t('common.activeBtn')"
                       :inactive-text="$t('common.inactiveBtn')"
                       inline-prompt
                       style="--el-switch-on-color: #762ADB; --el-switch-off-color: #CCCFD5"
                       v-model="scope.row.status"
                       :active-value="1"
                       :inactive-value="0"
                       v-hasPerm="['sys:role:updateStatus']"
                       @change="changeRoleStatus(scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column :label="$t('roleManagement.label.operate')" fixed="right" width="250px">
          <template #default="scope">
            <el-button  type="primary" @click="handleEdit(scope.row)" link v-hasPerm="['sys:role:edit']">
              {{ $t('common.edit') }}
            </el-button>
            <el-button  type="text" @click="deleteRole(scope.row)" link v-hasPerm="['sys:role:delete']">
              {{ $t('common.delete') }}
            </el-button>
          </template>
        </el-table-column>
        <template #empty>
        <Empty/>
      </template>
      </el-table>
      <pagination
          v-show="tableTotal > 0"
          :total="tableTotal"
          v-model:page="searchForm.page"
          v-model:limit="searchForm.limit"
          @pagination="getTableList"
      />
    </div>
    <Edit class="userEditDialog"
          v-model="roleDialog.open"
          ref="cmtRole"
          :title="roleDialog.title"
          :edit-type="roleDialog.type"
          @submitted="submitRole"
    />
  </div>
</template>

<script setup name="roleManagement">
import {getPageList, delRole, updateRole,updateEnableStatus} from '@/api/organization.js'
import Edit from './edit.vue'
import {ElMessage} from "element-plus";
const {proxy} = getCurrentInstance()

let data = reactive({
  searchForm: initForm(),
  tableLoading: false,
  tableTotal: 0,
  tableData: [],
  roleStatusList: [
    {
      statusId: 1,
      statusName: proxy.$t('roleManagement.operation.enable')
    }, {
      statusId: 0,
      statusName: proxy.$t('roleManagement.operation.disable')
    }
  ]
})

let roleDialog = ref({
  open: false,
  title: '',
  type: ''
})

let {searchForm, tableData, tableTotal, tableLoading, roleStatusList} = toRefs(data);

function initForm() {
  return {
    roleName: '',
    status: '',
    page: 1,
    limit: 20,
  }
}

function getTableList() {
  tableLoading.value = true
  let params = {
    ...searchForm.value,
    platformType: "platform",
    
  }
  getPageList(params)
      .then(res => {
        if (res.code !== 0) {
        } else {
          tableData.value = res.data.records
          tableTotal.value = parseInt(res.data.total)
        }
      }).finally(() => tableLoading.value = false)
}

searchEvent()

function searchEvent() {
  searchForm.value.page = 1
  getTableList()
}

let searchRef = ref()

function resetEvent() {
  searchForm.value = initForm()
  getTableList()
}

let cmtRole = ref(null)

// 新增角色
function openAdd() {
  cmtRole.value.setEditType("add")
  roleDialog.value.open = true
  roleDialog.value.title = proxy.$t('roleManagement.title.addRoleTitle')
  cmtRole.value.queryAllMenuList()
}

// 编辑角色
function handleEdit(row) {
  cmtRole.value.setEditType("edit")
  cmtRole.value.setFormData(row)
  roleDialog.value.open = true
  roleDialog.value.title = proxy.$t('roleManagement.title.editRoleTitle')
  cmtRole.value.queryRoleMenuList()
}

function deleteRole(row) {
  if(row.status==1){
      return  ElMessage.error(proxy.$t('roleManagement.message.deleteNotTips'))
  }
  ElMessageBox.confirm(proxy.$t('roleManagement.message.deleteTips'), proxy.$t('common.tipTitle'), {
    confirmButtonText: proxy.$t('common.confirm'),
    cancelButtonText: proxy.$t('common.cancel'),
    type: "warning",
  }).then(() => {
    let params = {
      platformType: "platform",
      roleId: row.roleId
    }
    delRole(params).then(res => {
      if (res.code === 0) {
        ElMessage.success(proxy.$t('common.deleteTips'))
        searchEvent()
      } else {
        ElMessage.error(res.message)
      }
    })
  });
}

function submitRole() {
  ElMessage.success( proxy.$t('common.successTips'))
  searchEvent()
}

function changeRoleStatus(row) {
  let flag = row.status
  row.status = row.status === 0 ? 1 : 0//保持switch点击前的状态
    let params = {
      ...row,
      status: flag,
      platformType: "platform",
    }
    if(flag===0){
        ElMessageBox.confirm(proxy.$t('roleManagement.message.disableTips'), proxy.$t('common.tipTitle'), {
            confirmButtonText: proxy.$t('common.confirm'),
            cancelButtonText: proxy.$t('common.cancel'),
            type: "warning",
        }).then(() => {
          updateEnableStatus(params).then(res => {
                if (res.code === 0) {
                    ElMessage.success(proxy.$t('roleManagement.message.disableSucess'))
                    getTableList()
                } else {
                    ElMessage.success(proxy.$t('roleManagement.message.disableFail'))
                }
            })
        })
    }else{
      updateEnableStatus(params).then(res => {
            if (res.code === 0) {
                ElMessage.success(proxy.$t('roleManagement.message.enableSucess'))
                getTableList()
            } else {
                ElMessage.success(proxy.$t('roleManagement.message.enableFail'))
            }
        })
}
}
</script>

<style scoped lang="scss">
.el-select {
  width: 220px;
}
.roleManagement{
  .userEditDialog {
    :deep(.el-select) {
      width: 380px;
    }

    :deep(.el-input) {
      width: 380px;
    }
  }
}

</style>
