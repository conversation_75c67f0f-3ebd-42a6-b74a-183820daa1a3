<template>
  <div v-if="!item.hidden" class="el-menu__route">
    <template v-if="hasOneShowingChild(item.children, item) && (!onlyOneChild.children)">
      <app-link v-if="onlyOneChild.menuId" :to="resolvePath(onlyOneChild.path, onlyOneChild.query)">
        <el-menu-item :index="resolvePath(onlyOneChild.path)" :class="{ 'submenu-title-noDropdown': !isNest }" class="modify-menu-item">
          <svg-icon v-if="onlyOneChild.icon" :icon-class="onlyOneChild.icon"/>
          <template #title>
            <SidebarItemTitle
              :title="hasTitle(onlyOneChild.menuName)"
            />
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu class="modify-submenu" v-else ref="subMenu" :index="resolvePath(item.path)" teleported>
      <template v-if="item.menuId" #title>
        <SidebarItemTitle
          :icon="item.icon"
          :title="hasTitle(item.menuName)"
        />
      </template>

      <sidebar-item
        v-for="(child, index) in item.children"
        :key="child.path + index"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup>
import { isExternal } from '@/utils/validate'
import AppLink from './Link'
import SidebarItemTitle from './SidebarItemTitle'
import { getNormalPath } from '@/utils/index'

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true
  },
  isNest: {
    type: Boolean,
    default: false
  },
  basePath: {
    type: String,
    default: ''
  }
})

const onlyOneChild = ref({});

function hasOneShowingChild(children = [], parent) {
  if (!children) {
    children = [];
  }
  const showingChildren = children.filter(item => {
    if (item.hidden) {
      return false
    } else {
      // Temp set(will be used if only has one showing child)
      onlyOneChild.value = item
      return true
    }
  })

  // 当只有一个子路由器时，显示子路由器
  // if (showingChildren.length === 1) {
  //   return true
  // }

  // 如果没有要显示的子路由器，则显示父路由器
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: '', noShowingChildren: true }
    return true
  }

  return false
};

function resolvePath(routePath, routeQuery) {
  if (isExternal(routePath)) {
    return routePath
  }
  if (isExternal(props.basePath)) {
    return props.basePath
  }
  if (routeQuery) {
    let query = JSON.parse(routeQuery);
    return { path: getNormalPath(props.basePath + '/' + routePath), query: query }
  }
  return getNormalPath(props.basePath + '/' + routePath)
}

function hasTitle(title){
  return title;
  /*if (title.length > 1) {
    return title;
  } else {
    return "";
  }*/
}
</script>

<style lang="scss" scoped>


.modify-menu-item {
  position: relative;
  color: #151719;
  &.is-active {
    &::before {
      content: " ";
      position: absolute;
      display: block;
      z-index: 100;
      top: 50%;
      margin-left: -8px;
      transform: translateY(-50%);
      width: 4px;
      height: 4px;
      background: #FFFFFF;
      border-radius: 50%;
    }
  }
}

:deep(.el-menu-item){
  font-size: 13px !important;
}

</style>
