<!--<template>-->
<!--  &lt;!&ndash; 上传组件 &ndash;&gt;-->
<!--  <el-upload-->
<!--    :file-list="fileList"-->
<!--    v-loading="loading"-->
<!--    :show-file-list="showFileList"-->
<!--    :before-upload="handleBeforeUpload"-->
<!--    :http-request="uploadFile"-->
<!--    :accept="accept"-->
<!--    :list-type="listType"-->
<!--    :multiple="multiple"-->
<!--    :limit="limit"-->
<!--    :on-exceed="handleExceed"-->
<!--    :on-remove="handleRemove"-->
<!--    :on-preview="handlePreview"-->
<!--    :disabled="disabled"-->
<!--    :class="['upload-files-wrapper',{ 'disabled': fileList.length >= limit}]"-->
<!--  >-->
<!--    <template #default>-->
<!--      <slot name="default" :fileList="fileList">-->
<!--        <template v-if="listType==='picture-card'">-->
<!--          <div class="picture-card-plus" :style="imageSize">-->
<!--            <el-icon>-->
<!--              <Plus />-->
<!--            </el-icon>-->
<!--          </div>-->

<!--        </template>-->
<!--        <template v-else>-->
<!--          <el-button :disabled="disabled">-->
<!--            <el-icon class="icon-button">-->
<!--              <UploadFilled />-->
<!--            </el-icon>-->
<!--            选择文件-->
<!--          </el-button>-->
<!--        </template>-->
<!--      </slot>-->
<!--    </template>-->
<!--    <template #file="{ file }" v-if="listType==='picture-card'">-->
<!--      <div class="picture-card-preview">-->
<!--        <template v-if="isFileType(file.url)==='image'">-->
<!--          <img class="picture-card-preview-thumbnail" :style="imageSize" :src="file.url"-->
<!--               alt="" />-->
<!--        </template>-->
<!--        <template v-if="isFileType(file.url)==='video'">-->
<!--          <video class="picture-card-preview-thumbnail" :style="imageSize">-->
<!--            <source :src="file.url" />-->
<!--          </video>-->
<!--        </template>-->
<!--        <div class="picture-card-preview-mask">-->
<!--          <div class="picture-card-preview-actions">-->
<!--                    <span class="icon icon-preview" @click.stop="handlePreview(file)">-->
<!--                         <el-icon><zoom-in /></el-icon>-->
<!--                      </span>-->
<!--            <template v-if="!disabled">-->
<!--                            <span class="icon icon-delete" @click.stop="handleRemove(file)">-->
<!--                             <el-icon><Delete /></el-icon>-->
<!--                          </span>-->
<!--            </template>-->
<!--          </div>-->
<!--        </div>-->
<!--        <div class="picture-card-preview-status">-->
<!--                    <span class="icon icon-check">-->
<!--                         <el-icon><Check /></el-icon>-->
<!--                    </span>-->

<!--        </div>-->
<!--      </div>-->
<!--    </template>-->

<!--    <template #tip>-->
<!--      <slot name="tip" :fileList="fileList">-->
<!--        <div class="upload-tips" v-if="tipsText">-->
<!--          {{ tipsText }}-->
<!--        </div>-->
<!--      </slot>-->
<!--    </template>-->

<!--  </el-upload>-->
<!--  <template v-if="preview.viewType==='image'">-->
<!--    <ImgPreview v-model="preview.visible" :urlList="preview.urlList" />-->
<!--  </template>-->
<!--  <template v-else-if="preview.viewType==='video'">-->
<!--    <el-dialog v-if="preview.visible" v-model="preview.visible">-->
<!--      <video autoplay controls :muted="true">-->
<!--        <source :src="preview.urlList[0]" />-->
<!--      </video>-->
<!--    </el-dialog>-->
<!--  </template>-->
<!--</template>-->

<!--<script setup lang="ts">-->
<!--import { ElMessage } from 'element-plus'-->
<!--import { commonUpload } from "@/api/oss";-->

<!--const props = defineProps({-->
<!--  folder:{-->
<!--    type: String,-->
<!--    default: 'omsImage'-->
<!--  },-->
<!--  // 是否为敏感文件 ‘’ 为否  ‘PRIVATE’ 为是-->
<!--  isPrivate:{-->
<!--    type:String,-->
<!--    default:''-->
<!--  },-->
<!--  modelValue: {-->
<!--    type: [String, Array] as any,-->
<!--    default: () => []-->
<!--  },-->
<!--  accept: {  //接受上传的文件类型.jpg, .jpeg, .png, .bmp, .doc, .docx, .pdf, .xls, .xlsx, .mp4, .avi-->
<!--    type: String,-->
<!--    default: '.jpg, .jpeg, .png, .bmp'-->
<!--  },-->
<!--  fileSize: {-->
<!--    type: Number,-->
<!--    default: 10 // 限制大小单位M-->
<!--  },-->
<!--  limit: { //允许上传文件的最大数量-->
<!--    type: Number,-->
<!--    default: 5-->
<!--  },-->
<!--  multiple: { //是否支持多选文件-->
<!--    type: Boolean,-->
<!--    default: true-->
<!--  },-->
<!--  listType: {  //文件列表的类型 text/picture/picture-card-->
<!--    type: String as any,-->
<!--    default: 'picture-card'-->
<!--  },-->
<!--  showFileList: { //是否显示文件列表-->
<!--    type: Boolean,-->
<!--    default: true-->
<!--  },-->
<!--  tipsText: { // 提示文本-->
<!--    type: String,-->
<!--    default: ''-->
<!--  },-->
<!--  previewSize: { //预览图和上传区域的尺寸-->
<!--    type: [Number, String, Array] as any,-->
<!--    default: 100-->
<!--  },-->
<!--  disabled: { //是否禁用-->
<!--    type: Boolean,-->
<!--    default: false-->
<!--  },-->
<!--  showFileName: { //上传路径是否携带文件名-->
<!--    type: Boolean,-->
<!--    default: false-->
<!--  }-->
<!--})-->

<!--const emit = defineEmits(['update:modelValue', 'input'])-->


<!--const state = reactive({-->
<!--  loading: false,-->
<!--  modelType: '',-->
<!--  fileList: [],-->
<!--  preview: {-->
<!--    visible: false,-->
<!--    urlList: [],-->
<!--    viewType: ''-->
<!--  }-->
<!--}) as any-->
<!--const {-->
<!--  loading,-->
<!--  modelType,-->
<!--  fileList,-->
<!--  preview-->
<!--} = toRefs(state)-->


<!--const imageSize = computed(() => {-->
<!--  const size = props.previewSize-->
<!--  let width = size, height = size-->
<!--  if (size instanceof Array) {-->
<!--    width = size[0] + 'px'-->
<!--    height = size[1] + 'px'-->
<!--  } else if (typeof size === 'string') {-->
<!--    width = size-->
<!--    height = size-->
<!--  } else if (typeof size === 'number') {-->
<!--    width = size + 'px'-->
<!--    height = size + 'px'-->
<!--  }-->
<!--  return Object.assign({}, { width, height })-->
<!--})-->

<!--/**-->
<!-- * 自定义图片上传-->
<!-- *-->
<!-- * @param options-->
<!-- */-->
<!--async function uploadFile(options: any): Promise<any> {-->
<!--  loading.value = true-->
<!--  const parts = options.file.name.split('.')-->
<!--  const fileType = parts.length > 1 ? parts.pop() : ''-->
<!--  let uploadType-->
<!--  if (isFileType(options.file.name) === 'image') {-->
<!--    uploadType = 'image'-->
<!--  } else {-->
<!--    uploadType = fileType-->
<!--  }-->
<!--  await commonUpload(options.file,props.folder,props.isPrivate).then((res: any) => {-->
<!--    if (props.showFileName){-->
<!--      res.name= getFileName(res.name)-->
<!--    }-->
<!--    fileList.value.push(res)-->
<!--    updateModelValue()-->
<!--    emit('input', fileList.value)-->
<!--  }).finally(() => {-->
<!--    loading.value = false-->
<!--  })-->

<!--}-->

<!--/**-->
<!-- * 限制用户上传文件的格式和大小-->
<!-- */-->
<!--function handleBeforeUpload(file: any) {-->
<!--  const parts = file.name.split('.')-->
<!--  const fileType = parts.length > 1 ? '.' + parts.pop() : ''-->

<!--  const isLimitType = props.accept.includes(fileType)-->
<!--  if (!isLimitType) {-->
<!--    ElMessage.error(`上传格式不正确，支持的格式为${props.accept}`)-->
<!--    return false-->
<!--  }-->
<!--  const fileSize = file.size / 1024 / 1024 <= props.fileSize-->
<!--  if (!fileSize) {-->
<!--    ElMessage.error(`上传文件不能大于${props.fileSize}M`)-->
<!--    return false-->
<!--  }-->

<!--  return true-->
<!--}-->

<!--/**-->
<!-- * 当超出限制时-->
<!-- */-->
<!--function handleExceed() {-->
<!--  ElMessage.error(`最多上传${props.limit}个`)-->
<!--}-->


<!--/**-->
<!-- * 移除文件-->
<!-- */-->
<!--function handleRemove(uploadFile: any, uploadFiles?: any) {-->
<!--  const indexFlag: number = fileList.value.findIndex((item: any) => item.url === uploadFile.url)-->
<!--  if (indexFlag >= 0) {-->
<!--    fileList.value.splice(indexFlag, 1)-->
<!--  }-->
<!--  updateModelValue()-->
<!--  emit('input', fileList.value)-->
<!--}-->

<!--/**-->
<!-- * 击文件列表中已上传的文件-->
<!-- */-->
<!--function handlePreview(uploadFile: any) {-->
<!--  if (isFileType(uploadFile.url) === 'image') {-->
<!--    preview.value.viewType = 'image'-->
<!--    preview.value.urlList = [uploadFile.url]-->
<!--    preview.value.visible = true-->
<!--  } else if (isFileType(uploadFile.url) === 'video') {-->
<!--    preview.value.viewType = 'video'-->
<!--    preview.value.urlList = [uploadFile.url]-->
<!--    preview.value.visible = true-->
<!--  } else {-->
<!--    downloadFileByUrl(uploadFile.url, uploadFile.name)-->
<!--  }-->
<!--}-->

<!--/**-->
<!-- * 初始化数据-->
<!-- */-->
<!--function initData() {-->
<!--  let data = props.modelValue-->
<!--  if (data instanceof Array) {-->
<!--    if (isObjectArray(data)) {-->
<!--      modelType.value = 'objectArray'-->
<!--      fileList.value = data.map((item: any) => {-->
<!--        return item-->
<!--      })-->
<!--    } else {-->
<!--      modelType.value = 'array'-->
<!--      fileList.value = data.map((item: any) => {-->
<!--        return { name: getFileName(item), url: item }-->
<!--      })-->
<!--    }-->

<!--  } else {-->
<!--    modelType.value = 'string'-->
<!--    const urlArr = data ? data.split(',') : []-->
<!--    fileList.value = urlArr.map((item: any) => {-->
<!--      return { name: getFileName(item), url: item }-->
<!--    })-->

<!--  }-->
<!--}-->


<!--/**-->
<!-- *  更新modelValue值-->
<!-- */-->
<!--function updateModelValue() {-->
<!--  let data = fileList.value.map((item: any) => {-->
<!--    return item.url-->
<!--  })-->
<!--  if (modelType.value === 'objectArray') {-->
<!--    emit('update:modelValue', fileList.value)-->
<!--  } else if (modelType.value === 'array') {-->
<!--    emit('update:modelValue', data)-->
<!--  } else {-->
<!--    emit('update:modelValue', data.join(','))-->
<!--  }-->
<!--}-->


<!--onMounted(() => {-->
<!--  initData()-->
<!--})-->

<!--watch(() => props.modelValue, () => {-->
<!--    initData()-->
<!--  },-->
<!--  { deep: true }-->
<!--)-->

<!--const isObjectArray = (value: any): boolean => {-->
<!--  return Array.isArray(value) && value.length > 0 && value.every(item => item instanceof Object);-->
<!--}-->
<!--/**-->
<!-- * 判断文件类型-->
<!-- * @param url-->
<!-- * @returns {string}-->
<!-- */-->
<!--const isFileType = (url: any) => {-->
<!--  // 定义常见的视频和图片扩展名-->
<!--  const videoRegex = /\.(mp4|avi|mkv|mov|flv|wmv)$/i-->
<!--  const imageRegex = /\.(jpg|jpeg|png|gif|bmp|svg)$/i-->
<!--  const fileRegex = /\.(pdf|doc|docx|xls|xlsx|ppt|txt)$/i-->
<!--  // 判断文件扩展名是否属于视频或图片-->
<!--  if (videoRegex.test(url)) {-->
<!--    return 'video'-->
<!--  } else if (imageRegex.test(url)) {-->
<!--    return 'image'-->
<!--  } else if (fileRegex.test(url)) {-->
<!--    return 'file'-->
<!--  } else {-->
<!--    return 'unknown'-->
<!--  }-->
<!--}-->

<!--const downloadFileByUrl = (url:string, fileName:string) => {-->
<!--  const downLink = (href: string) => {-->
<!--    const link = document.createElement('a');-->
<!--    link.target = '_blank';-->
<!--    link.href = href;-->
<!--    link.download = fileName;-->
<!--    link.click();-->
<!--    window.URL.revokeObjectURL(href);-->
<!--  };-->
<!--  try{-->
<!--    const res = new XMLHttpRequest()-->
<!--    res.open('GET', url, true)-->
<!--    res.responseType = 'blob'-->
<!--    res.onload = function () {-->
<!--      const newUrl = window.URL.createObjectURL(res.response)-->
<!--      downLink(newUrl)-->
<!--    }-->
<!--    res.send()-->
<!--  }catch(e){-->
<!--    downLink(url)-->
<!--  }-->
<!--}-->

<!--/**-->
<!-- * 获取url文件名-->
<!-- * @param url-->
<!-- */-->
<!--function getFileName(url: string) {-->
<!--  url = decodeURIComponent(url)-->
<!--  let name: string;-->
<!--  if (url.includes('__')) {-->
<!--    const part = url.split('__');-->
<!--    name = part.slice(1).join('__');-->
<!--  } else {-->
<!--    name = url.split('/').pop()!;-->
<!--  }-->
<!--  const parts = name.split('.');-->
<!--  const extension = parts.length > 1 ? parts.pop()! : '';-->
<!--  const fileName = parts.join('.');-->
<!--  return `${fileName}.${extension}`-->
<!--}-->
<!--</script>-->

<!--<style scoped lang="scss">-->

<!--.upload-files-wrapper {-->
<!--  width: 100%;-->

<!--  :deep(.el-upload) {-->
<!--    display: block;-->

<!--  }-->

<!--  :deep(.el-upload-list&#45;&#45;text) {-->
<!--    margin: 0;-->
<!--  }-->

<!--  :deep(.el-upload-list__item .el-icon&#45;&#45;close-tip) {-->
<!--    display: none !important;-->
<!--  }-->

<!--  :deep( .el-upload-list__item-thumbnail) {-->
<!--    object-fit: cover;-->
<!--  }-->

<!--  :deep(.el-upload&#45;&#45;picture-card) {-->
<!--    width: unset;-->
<!--    height: unset;-->
<!--    line-height: normal;-->
<!--    border: none;-->
<!--    text-align: left;-->
<!--    background-color: unset;-->

<!--  }-->

<!--  :deep(.el-upload-list&#45;&#45;picture-card .el-upload-list__item) {-->
<!--    width: unset;-->
<!--    height: unset;-->
<!--  }-->

<!--  &.disabled {-->
<!--    :deep(.el-upload ) {-->
<!--      display: none !important;-->
<!--    }-->
<!--  }-->

<!--  .picture-card-plus {-->
<!--    display: flex;-->
<!--    align-items: center;-->
<!--    justify-content: center;-->
<!--    height: 100%;-->
<!--    font-size: 24px;-->
<!--    background-color: #fafafa;-->
<!--    border: 1px dashed #cdd0d6;-->
<!--    border-radius: 6px;-->

<!--  }-->

<!--  .icon-button {-->
<!--    margin-right: 5px;-->
<!--  }-->

<!--  .picture-card-preview {-->
<!--    position: relative;-->
<!--    overflow: hidden;-->

<!--    .picture-card-preview-thumbnail {-->
<!--      width: 100%;-->
<!--      height: 100%;-->
<!--      object-fit: cover;-->
<!--      vertical-align: bottom;-->
<!--    }-->

<!--    .picture-card-preview-mask {-->
<!--      opacity: 0;-->
<!--      position: absolute;-->
<!--      top: 0;-->
<!--      width: 100%;-->
<!--      height: 100%;-->
<!--      background-color: rgba(0, 0, 0, 0.5);-->
<!--      transition: all 0.3s;-->

<!--      &:hover {-->
<!--        opacity: 1;-->
<!--      }-->

<!--      .picture-card-preview-actions {-->
<!--        width: 100%;-->
<!--        height: 100%;-->
<!--        display: flex;-->
<!--        justify-content: center;-->
<!--        align-items: center;-->
<!--        font-size: 20px;-->
<!--        color: #ffffff;-->

<!--        .icon {-->
<!--          margin: 0 5px;-->
<!--        }-->


<!--      }-->
<!--    }-->

<!--    .picture-card-preview-status {-->
<!--      position: absolute;-->
<!--      right: -15px;-->
<!--      top: -6px;-->
<!--      width: 40px;-->
<!--      height: 24px;-->
<!--      background: #67C23A;-->
<!--      text-align: center;-->
<!--      transform: rotate(45deg);-->
<!--      line-height: normal;-->

<!--      .icon {-->
<!--        position: absolute;-->
<!--        right: 12px;-->
<!--        top: 10px;-->
<!--        color: #fff;-->
<!--        font-size: 12px;-->
<!--        transform: rotate(-45deg);-->
<!--        font-weight: 600;-->
<!--      }-->
<!--    }-->
<!--  }-->

<!--  .upload-tips {-->
<!--    color: #999;-->
<!--    font-size: 13px;-->
<!--  }-->
<!--}-->
<!--</style>-->
