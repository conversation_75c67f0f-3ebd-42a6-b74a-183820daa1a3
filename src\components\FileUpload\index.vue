<template>
  <div class="upload-file" id="uploadWrap">
    <el-upload
      multiple
      :http-request="uploadFile"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      class="upload-file-uploader"
      ref="fileUpload"
      v-if="fileList.length < limit"
    >
      <!-- 上传按钮 -->
      <el-button type="primary">选取文件</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div class="el-upload__tip" v-if="showTip && fileList.length < limit">
      请上传
      <template v-if="fileSize"> 大小不超过 <b style="color: #f56c6c">{{ fileSize }}MB</b> </template>
      <template v-if="fileType"> 格式为 <b style="color: #f56c6c">{{ fileType.join("/") }}</b> </template>
      的文件
    </div>
    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul" v-if="fileList.length > 0">
      <li :key="file.uid" class="el-upload-list__item ele-upload-list__item-content" v-for="(file, index) in fileList">
        <el-link :underline="false" target="_blank" @click="handleOpen(file.url)" style="min-width: 120px;">
          <span class="el-icon-document"> {{ getFileName(file.name) }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" @click="handleDelete(index)" type="danger" v-if="!isDisabled">删除</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { commonUpload } from "@/api/oss";
import { getSignedUrl } from "@/api/common.js"
const props = defineProps({
  modelValue: [String, Object, Array],
  // 数量限制
  limit: {
    type: Number,
    default: 5,
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 5,
  },
  // 是否为敏感文件 ‘’ 为否  ‘PRIVATE’ 为是
  isPrivate:{
    type:String,
    default:''
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ["doc", "xls", "ppt", "txt", "pdf"],
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  formRef: {
    type: Object,
    default: () => {
    }
  },
  name: {
    type: String,
    default: ''
  },
  isDisabled:{
     type: Boolean,
     default: false
  },
  folder:{
    type: String,
    default: 'omsFile'
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits();
const number = ref(0);
const uploadList = ref([]);
const fileList = ref([]);
const showTip = computed(
  () => props.isShowTip && (props.fileType || props.fileSize)
);

watch(() => props.modelValue, val => {
  if (val) {
    let temp = 1;
    // 首先将值转为数组
    const list = Array.isArray(val) ? val : props.modelValue.split(',');
    console.log(list);
    // 然后将数组转为对象数组
    fileList.value = list.map(item => {
      if (typeof item === "string") {
        item = { name: item, url: item };
      }
      item.uid = item.uid || new Date().getTime() + temp++;
      return item;
    });
  } else {
    fileList.value = [];
    return [];
  }
},{ deep: true, immediate: true });

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    const fileExt = fileName[fileName.length - 1];
    const isTypeOk = props.fileType.indexOf(fileExt.toLowerCase()) >= 0;
    if (!isTypeOk) {
      proxy.$modal.msgError(`文件格式不正确, 请上传${props.fileType.join("/")}格式文件!`);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`上传文件大小不能超过 ${props.fileSize} MB!`);
      return false;
    }
  }
  return true;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`上传文件数量不能超过 ${props.limit} 个!`);
}

// 上传失败
function handleUploadError(err) {
  proxy.$modal.msgError("上传文件失败");
}

/**
 * 自定义文件上传
 *
 * @param options
 */
async function uploadFile(options){
  proxy.$modal.loading("正在上传文件，请稍候...");
  await commonUpload(options.file,props.folder,props.isPrivate).then(res => {
    console.log(res);
    if(res.res.status === 200){
      proxy.$modal.msgSuccess("上传成功");
      number.value++;
      uploadList.value.push({ name: res.name, url: res.url });
      uploadedSuccessfully();
      props.formRef.validateField(props.name)
    }else{
      proxy.$modal.msgError(res.msg);
        number.value--;
        // proxy.$refs.fileUpload.handleRemove(file);
        props.formRef.validateField(props.name);
        uploadedSuccessfully();
    }
  }).finally(() => {
    proxy.$modal.closeLoading();
  })

}
// 上传成功回调
function handleUploadSuccess(res, file) {
  console.log("上传成功============")
  // if (res.code === 200) {
  //   uploadList.value.push({ name: res.fileName, url: res.fileName });
  //   uploadedSuccessfully();
  // } else {
  //   number.value--;
  //   proxy.$modal.closeLoading();
  //   proxy.$modal.msgError(res.msg);
  //   proxy.$refs.fileUpload.handleRemove(file);
  //   props.formRef.validateField(props.name);
  //   uploadedSuccessfully();
  // }
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1);
  emit("update:modelValue", listToString(fileList.value));
}
function handleOpen(url){
  if(props.isPrivate === 'PRIVATE'){
    getSignedUrl({filePath:url})
      .then((res) => {
        if(res.code === 0){
          window.open(res.data.data)
        }
     })
  }else{
    window.open(url)
  }
  
}
// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    console.log(fileList.value)
    emit("update:modelValue", listToString(fileList.value));
    proxy.$modal.closeLoading();
  }
}

// 获取文件名称
function getFileName(name) {
  // 如果是url那么取最后的名字 如果不是直接返回
  if (name.lastIndexOf("/") > -1) {
    return decodeURIComponent(name.slice(name.lastIndexOf("/") + 1));
  } else {
    return decodeURIComponent(name);
  }
}

// 对象转成指定字符串分隔
function listToString(list, separator) {
  let strs = "";
  separator = separator || ",";
  for (let i in list) {
    if (list[i].url) {
      strs += list[i].url + separator;
    }
  }
  return strs != '' ? strs.substr(0, strs.length - 1) : '';
}
</script>

<style scoped lang="scss">
#uploadWrap{
  :deep(.upload-file-uploader) {
    margin-bottom: 5px;
  }
  :deep(.upload-file-list .el-upload-list__item) {
    //border: 1px solid #e4e7ed;
    //line-height: 2;
    margin-bottom: 10px;
    position: relative;
    text-decoration: underline;
  }
  :deep(.upload-file-list .ele-upload-list__item-content) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: inherit;
  }
  :deep(.ele-upload-list__item-content-action .el-link) {
    margin-right: 10px;
  }
  :deep(.ele-upload-list__item-content-action){
    margin-left: 10px;
  }
}

</style>
