<template>
  <div class="page tenantsManagement">
    <div class="queryParamsForm">
      <el-form :inline="true" v-model="searchForm" ref="userForm">
        <el-form-item
          prop="tenantName"
          :label="$t('tenantsManagement.label.name')"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model.trim="searchForm.tenantName"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="status"
          :label="$t('tenantsManagement.label.status')"
        >
          <el-select
            v-model="searchForm.status"
            clearable
            :placeholder="$t('common.placeholder.selectTips')"
          >
            <el-option
              v-for="item in userStatusList"
              :key="item.statusId"
              :label="item.statusName"
              :value="item.statusId"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          prop="mobileLast"
          :label="$t('tenantsManagement.label.phone')"
        >
          <el-input
            type="text"
            :placeholder="$t('common.placeholder.inputTips')"
            v-model.trim="searchForm.mobileLast"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="tenantType"
          :label="$t('tenantsManagement.label.type')"
        >
          <!-- <el-input type="text" :placeholder="$t('common.placeholder.inputTips')" v-model.trim="searchForm.tenantType" clearable></el-input> -->
          <el-select
            clearable
            v-model="searchForm.tenantType"
            :placeholder="$t('common.placeholder.inputTips')"
          >
            <el-option
              v-for="item in tenantTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item class="btn-form">
          <el-button
            type="primary"
            @click="searchEvent"
            v-hasPermi="['sys:tenant:page']"
            >{{ $t("common.search") }}</el-button
          >
          <el-button @click="resetEvent">{{ $t("common.reset") }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-info">
      <div class="mb10">
        <el-button
          @click="openAdd"
          type="primary"
          v-hasPermi="['sys:tenant:add']"
          >{{ $t("tenantsManagement.button.addAccunt") }}</el-button
        >
      </div>
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        border
        style="width: 100%"
        :key="new Date()"
      >
        <el-table-column
          prop="tenantId"
          :label="$t('tenantsManagement.label.tenantCode')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="tenantName"
          :label="$t('tenantsManagement.label.tenantName')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          prop="tenantType"
          :label="$t('tenantsManagement.label.tenantType')"
          min-width="100"
        >
          <template #default="scope">
            <span>{{
              scope.row.tenantType === 1
                ? $t("tenantsManagement.label.signedTenant")
                : $t("tenantsManagement.label.experienceTenant")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="contacts"
          :label="$t('tenantsManagement.label.contactPerson')"
          min-width="100"
        ></el-table-column>
        <el-table-column
          prop="mobile"
          :label="$t('tenantsManagement.label.phone')"
          min-width="120"
        >
            <template #default="scope">
              <span class="encryptBox">
                    {{scope.row.countryAreaCode}}
                    <span v-if="scope.row.mobile && scope.row.mobile.length <= 4">{{scope.row.mobile}}</span>
                    <span v-else>
                      {{scope.row.mobile}}
                      <el-icon
                          v-if="scope.row.mobile"
                          v-hasPermi="['sys:tenant:mobile']"
                          @click="scope.row.mobilePhoneShow ? getRealPhone(scope.row.tenantId,scope.$index) :''"
                          class="encryptBox-icon"
                          color="#762ADB"
                          size="16"
                      >
                        <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                </span>
            </template>

        </el-table-column>
        <el-table-column
          prop="account"
          :label="$t('tenantsManagement.label.loginAccount')"
          min-width="120"
        ></el-table-column>
        <el-table-column
          :label="$t('tenantsManagement.label.usagePeriod')"
          min-width="200"
        >
          <template #default="scope">
            <span
              >{{ formatDate(scope.row.startDate) }} 至
              {{ formatDate(scope.row.endDate) }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('tenantsManagement.label.status')"
          min-width="100"
          v-hasPerm="['sys:tenant:status']"
        >
          <template #default="scope">
            <el-switch
              :active-text="$t('common.activeBtn')"
              :inactive-text="$t('common.inactiveBtn')"
              inline-prompt
              style="
                --el-switch-on-color: #762ADB;
                --el-switch-off-color: #cccfd5;
              "
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="changeStatus(scope.row)"
            >
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('common.handle')"
          fixed="right"
          min-width="180"
        >
          <template #default="scope">
            <el-button
              type="primary"
              @click="handleUpdateContact(scope.row)"
              v-hasPermi="['sys:tenat:update:contact']"
              link
            >
              {{ $t("tenantsManagement.label.updateContact") }}
            </el-button>
            <el-button
              type="primary"
              v-hasPermi="['sys:tenant:detail']"
              @click="handleDetail(scope.row)"
              link
            >
              {{ $t("common.detail") }}
            </el-button>
            <el-button
              v-if="scope.row.status === 0"
              v-hasPermi="['sys:tenant:update']"
              type="primary"
              @click="handleEdit(scope.row)"
              link
            >
              {{ $t("common.edit") }}
            </el-button>
          </template>
        </el-table-column>
        <template #empty>
          <Empty />
        </template>
      </el-table>
      <pagination
        v-show="tableTotal > 0"
        :total="tableTotal"
        v-model:page="searchForm.page"
        v-model:limit="searchForm.limit"
        @pagination="getTableList"
      />
    </div>
    <el-drawer
      v-model="userContact.visible"
      :title="'更新联系人'"
      :size="800"
      :destroy-on-close="true"
    >
      <div v-loading="userContact.loading">
        <UpdateContact
          ref="cmtUser"
          :tenantId="userContact.data.tenantId"
          :contact-info="userContact.data"
          @submitted="submitUser"
        />
      </div>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="userContact.visible = false">取消</el-button>
          <el-button type="primary" v-loading="userContact.loading" @click="handleContactSubmit"
            >确定</el-button
          >
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup name="tenantsManagement">
import { pageList, updateStatus ,getPhoneNum} from "@/api/tenants.js";
import { baseConfig } from "@/utils/config";
import UpdateContact from "./components/UpdateContact.vue";

const router = useRouter();
const { proxy } = getCurrentInstance();

const userContact = ref({
  visible: false,
  data: {},
  loading: false
});
let data = reactive({
  searchForm: initForm(),
  tableLoading: false,
  tableTotal: 0,
  tableData: [],
});

// 租户类型选项
const tenantTypeOptions = computed(() => [
  {
    label: proxy.$t("tenantsManagement.label.signedTenant"),
    value: 1,
  },
  {
    label: proxy.$t("tenantsManagement.label.experienceTenant"),
    value: 2,
  },
]);

let userDialog = ref({
  open: false,
  title: "",
});

let { searchForm, tableData, tableTotal, tableLoading, queryParams } =
  toRefs(data);

const userStatusList = ref([
  {
    statusId: " ",
    statusName: proxy.$t("common.statusEmun.all"),
  },
  {
    statusId: 0,
    statusName: proxy.$t("common.statusEmun.disable"),
  },
  {
    statusId: 1,
    statusName: proxy.$t("common.statusEmun.enable"),
  },
]);

function initForm() {
  return {
    tenantName: "",
    status: "",
    mobileLast: "",
    tenantType: "",
    page: 1,
    limit: 20,
  };
}

// 格式化日期
const formatDate = (timestamp) => {
  if (!timestamp) return "";
  const date = new Date(timestamp);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const handleContactSubmit = () => {
  userContact.value.loading = true;
  cmtUser.value.handleSubmit().then((res) => {
    if(res){
      userContact.value.visible = false;
      getTableList();
    }
  }).catch(() => {
  }).finally(() => {
    userContact.value.loading = false;

  });
};

function changeStatus(row) {
  let flag = row.status;
  row.status = row.status === 0 ? 1 : 0; //保持switch点击前的状态
  let params = {
    tenantId: row.tenantId,
    status: flag,
  };
  if (flag === 0) {
    ElMessageBox.confirm(
      proxy.$t("tenantsManagement.message.disableTips"),
      proxy.$t("common.tipTitle"),
      {
        confirmButtonText: proxy.$t("common.confirm"),
        cancelButtonText: proxy.$t("common.cancel"),
        type: "warning",
      }
    ).then(() => {
      updateStatus(params).then((res) => {
        if (res.code === 0) {
          ElMessage.success(
            proxy.$t("tenantsManagement.message.disableSucess")
          );
          getTableList();
        } else {
          ElMessage.success(proxy.$t("tenantsManagement.message.disableFail"));
        }
      });
    });
  } else {
    updateStatus(params).then((res) => {
      if (res.code === 0) {
        ElMessage.success(proxy.$t("tenantsManagement.message.enableSucess"));
        getTableList();
      } else {
        ElMessage.success(proxy.$t("tenantsManagement.message.enableFail"));
      }
    });
  }
}

function getTableList() {
  tableLoading.value = true;
  pageList(searchForm.value)
    .then((res) => {
      if (res.code !== 0) {
      } else {
        tableData.value = res.data.records.map((item, index) => {
          item.mobilePhoneShow = true;
          item.roles = item.baseRoleVOList
            ? item.baseRoleVOList.map((it) => it.roleName).join(",")
            : [];
          return { ...item };
        });
        tableTotal.value = parseInt(res.data.total);
      }
    })
    .finally(() => (tableLoading.value = false));
}

searchEvent();

onMounted(() => {
  proxy.$mitt.on('refreshTenantList', () => {
    getTableList()
  })
})

onUnmounted(() => {
  // 清理事件监听
  proxy.$mitt.off('refreshTenantList')
})

function searchEvent() {
  searchForm.value.page = 1;
  getTableList();
}

function resetEvent() {
  searchForm.value = initForm();
  getTableList();
}

let cmtUser = ref(null);

// 重置密码
function handleResetPassword(row) {
  ElMessageBox.confirm(
    proxy.$t("accountManagement.message.resetPasswordTips"),
    proxy.$t("common.tipTitle"),
    {
      confirmButtonText: proxy.$t("common.confirm"),
      cancelButtonText: proxy.$t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    let params = {
      userId: row.userId,
    };
    resetPassword(params).then((res) => {
      if (res.code === 0) {
        ElMessage.success(
          proxy.$t("accountManagement.message.resertPasswordSucess")
        );
        getTableList();
      } else {
        ElMessage.success(
          proxy.$t("accountManagement.message.resertPasswordFail")
        );
      }
    });
  });
}

// 新增角色
function openAdd() {
  router.push({ path: "/tenants/add", query: { id: "", editType: "add" } });
}

function handleEdit(row) {
  router.push({
    path: "/tenants/update",
    query: {
      id: row.tenantId,
      editType: "edit",
    },
  });
}

// 编辑联系人
function handleUpdateContact(row) {
  userContact.value.visible = true;
  userContact.value.data = row;
}

// 查看详情
function handleDetail(row) {
  router.push({
    path: "/tenants/detail",
    query: {
      id: row.tenantId,
      editType: "detail",
    },
  });
}

// 删除
function handleDelete(row) {
  if (row.status == 1) {
    return ElMessage.error(proxy.$t("accountManagement.message.deleteNotTips"));
  }
  ElMessageBox.confirm(
    proxy.$t("accountManagement.message.deleteTips"),
    proxy.$t("common.tipTitle"),
    {
      confirmButtonText: proxy.$t("common.confirm"),
      cancelButtonText: proxy.$t("common.cancel"),
      type: "warning",
    }
  ).then(() => {
    let params = {
      appId: baseConfig.appId,
      userId: row.userId,
      status: 2,
    };
    updateUserStatus(params).then((res) => {
      if (res.code === 0) {
        ElMessage.success(proxy.$t("accountManagement.message.deleteSucess"));
        searchEvent();
      } else {
        ElMessage.success(proxy.$t("accountManagement.message.deleteFail"));
      }
    });
  });
}

// 修改角色状态
function changeUserStatus(row) {
  let flag = row.status;
  row.status = row.status === 0 ? 1 : 0; //保持switch点击前的状态
  let params = {
    appId: baseConfig.appId,
    userId: row.userId,
    status: flag,
  };
  if (flag === 0) {
    ElMessageBox.confirm(
      proxy.$t("accountManagement.message.disableTips"),
      proxy.$t("common.tipTitle"),
      {
        confirmButtonText: proxy.$t("common.confirm"),
        cancelButtonText: proxy.$t("common.cancel"),
        type: "warning",
      }
    ).then(() => {
      updateUserStatus(params).then((res) => {
        if (res.code === 0) {
          ElMessage.success(
            proxy.$t("accountManagement.message.disableSucess")
          );
          getTableList();
        } else {
          ElMessage.success(proxy.$t("accountManagement.message.disableFail"));
        }
      });
    });
  } else {
    updateUserStatus(params).then((res) => {
      if (res.code === 0) {
        ElMessage.success(proxy.$t("accountManagement.message.enableSucess"));
        getTableList();
      } else {
        ElMessage.success(proxy.$t("accountManagement.message.enableFail"));
      }
    });
  }
}


function getRealPhone(id, index) {
    getPhoneNum({ tenantId: id })
        .then((data) => {
            tableData.value[index].mobile = data.data.mobile;
            tableData.value[index].mobilePhoneShow = false;
        })
        .finally(() => {});
}

function submitUser() {
  searchEvent();
}
</script>

<style scoped lang="scss">
.el-select {
  width: 220px;
}

.tenantsManagement {
  :deep(.el-dialog) {
    padding: 0 !important;
  }

  :deep(.el-dialog__header) {
    width: 100%;
    padding: 16px 32px;
    box-sizing: border-box;
    border-bottom: 1px solid #eceef1;
  }

  :deep(.el-dialog__body) {
    padding: 32px;
  }

  :deep(.el-dialog__footer) {
    padding-bottom: 12px;
    padding-right: 32px;
    text-align: right;
  }

  .userEditDialog {
    :deep(.el-select) {
      width: 380px;
    }

    :deep(.el-input) {
      width: 380px;
    }
  }
}

.preview-cell {
  display: flex;
  justify-content: space-between;
  padding-right: 4px;
}

.preview-handle {
  text-align: center;
  align-items: center;
  cursor: pointer;
}
</style>
