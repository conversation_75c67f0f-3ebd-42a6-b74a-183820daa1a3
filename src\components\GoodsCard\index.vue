<template>
  <div class="d2Goods">
    <el-image class="d2GoodsImage" style="width: 100px; height: 100px" :src="scope.row.url" fit="cover" />
    <el-space :size="4" direction="vertical" alignment="normal">
      <div class="d2GoodsName">韩国野生特大梭子蟹鲜活发货新鲜海捕</div>
      <TextCell label="SKU：">38473</TextCell>
      <TextCell label="规格：">450-500</TextCell>
    </el-space>
  </div>
</template>

<script setup>
let props = defineProps({
  item: Object,
})

</script>

<style lang="scss" scoped>
.d2Goods {
  display: flex;
  &Image {
    flex-shrink: 0;
    width: 100px;
    height: 100px;
    border-radius: 4px;
    margin-right: 8px;
  }
  &Name {
    font-weight: 600;
    font-size: 16px;
    color: #252829;
    line-height: 24px;
  }
}
</style>