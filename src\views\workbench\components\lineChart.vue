<template>
  <div
    :id="id"
    ref="lineChartRef"
    :style="{ width: '100%', height: '220px' }"
  ></div>
</template>
<script setup>
import * as echarts from 'echarts'
import {formatNumber} from "@/utils/index.js";

const {proxy} = getCurrentInstance()

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  id: {
    type: String,
    default: 'lineChart'
  },
  customColor: {
    type: String,
    default: '#FFC000'
  },
  lineLegend: {
    type: Array, // 数据数组
    default: () => []
  },
  customChartData: {
    type: Object,
    default: () => {
    }
  },
  lineStyle: {
    type: Object,
    default: () => {
    }
  },
  lineTitleStyle: {
    type: Object,
    default: () => {
    }
  }
})

let chartInstance = null;

const handleResize = () => {
  if (chartInstance) {
    nextTick(() => {
      chartInstance.resize();
    })
  }
}

function initLineChart() {
  // console.log(props.customChartData.xAxisData, props.customChartData.yAxisData)
  if (proxy.$refs.lineChartRef) {
    chartInstance = echarts.init(proxy.$refs.lineChartRef)
    const option = {
      title: {
        text: props.title || '',
        left: props.lineTitleStyle.left, // 水平居中
        top: '5%',
        textStyle: {
          color: "#252829", //文字颜色
          fontSize: 14,//文字大小
          fontWeight: 500,
        },
      },
      legend: {
        data: props.lineLegend, // 图例名称
        ...props.lineStyle, // 调整图例位置
        top: "5%", // 调整图例位置
        orient: 'vertical', // horizontal 水平布局 (vertical 垂直布局--显示底部图例时的常见设置)
        itemWidth: 18, // 图例标记的宽度
        itemHeight: 12, // 图例标记的高度
        itemGap: 10, // 图例项之间的间距
        textStyle: { // 图例文字的样式
          color: '#90979F',
          fontSize: 12
        }
      },
      tooltip: {  // 鼠标悬停时的显示样式
        trigger: 'axis',
        textStyle: {
          // color: "#FFFFFF", //文字颜色
          fontSize: 12,//文字大小
        },
        padding: [15],//边距 或： padding: 10
        // borderWidth: 1,//边框线粗细
        // borderColor: "#000000",//边框线颜色
        // backgroundColor: "rgba(0,0,0,0.75)",//背景色
        formatter: function (params) {
          let result = `${params[0].seriesName}<br/>`;
          params.forEach(item => {
            if (props.id === 'tradeLine') {
              result += `${item.marker}￥${formatNumber(item.value)}<br/>`;
            } else {
              result += `${item.marker}${item.value}<br/>`;
            }
          });
          return result;
        },
      },
      xAxis: {
        type: 'category',
        data: props.customChartData.xAxisData,
        boundaryGap: false, //设置为false代表是零刻度开始，设置为true代表离零刻度间隔一段距离
        axisLabel: { //x轴文字的配置
          show: true,
          textStyle: {
            color: "#90979F",
          },
          interval: 0,
          rotate: 36,
          rich: {
            italic: {
              fontStyle: 'italic', // 设置字体为斜体
              // 你可以在这里添加其他样式，如颜色、字体大小等
              // color: '#333',
              // fontSize: 12
            }
          }
        },
        axisLine: { // x轴线的颜色以及宽度
          show: false,
          lineStyle: {
            color: "#90979F",
            type: "solid"
          }
        },
      },
      yAxis: {
        type: 'value',
        minInterval: 1, // 最小刻度
        axisLine: { //y轴线的配置
          show: false, //是否展示
          lineStyle: {
            color: "#90979F",//y轴线的颜色（若只设置了y轴线的颜色，未设置y轴文字的颜色，则y轴文字会默认跟设置的y轴线颜色一致）
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: "#EDEFF2",
          }
        }
      },
      grid: {
        left: '5%',
        right: '3%',
        bottom: '1%',
        containLabel: true
      },
      color: props.customColor, // 折线点颜色
      series: [
        {
          name: props.lineLegend[0],
          type: 'line',
          data: props.customChartData.yAxisData,
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {offset: 0, color: props.customColor}, // 起始颜色
              {offset: 1, color: '#fff'}  // 结束颜色
            ])
          },
          lineStyle: {
            color: props.customColor // 折线颜色，可以单独设置
          },
        }]
    };
    // 设置实例参数
    chartInstance.setOption(option);
  }
}

onMounted(() => {
  initLineChart()
  window.addEventListener('resize', handleResize); // 添加 handleResize 监听器
})

onBeforeUnmount(() => {
  // console.log('组件即将卸载');
  window.removeEventListener('resize', handleResize); // 移除 handleResize 监听器
  if (chartInstance) {
    chartInstance.dispose(); // 销毁 ECharts 实例
  }
});

// 使用 watch 观察 props.lineData 的变化
watch(() => props.customChartData, (newData, oldData) => {
  // console.log(newData !== oldData, newData, oldData , '==使用 watch 观察')
  if (newData !== oldData && chartInstance) {
    // 当数据变化时，更新图表
    initLineChart()
  }
}, {deep: true}); // 使用 deep 选项来观察数组内部的变化

</script>

<style scoped lang="scss">
</style>
