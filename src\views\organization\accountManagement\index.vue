<template>
  <div class="page accountManagement">
    <div class="queryParamsForm">
      <el-form :inline="true" v-model='searchForm' ref='userForm'>
        <el-form-item prop='nickName' :label="$t('accountManagement.label.name')">
          <el-input type="text" :placeholder="$t('common.placeholder.inputTips')" v-model.trim="searchForm.nickName" clearable></el-input>
        </el-form-item>
          <el-form-item prop='mobile' :label="$t('accountManagement.label.phone')">
              <el-input type="text" :placeholder="$t('common.placeholder.inputTips')" v-model.trim="searchForm.mobile" clearable></el-input>
          </el-form-item>
        <el-form-item prop='status' :label="$t('accountManagement.label.status')">
          <el-select v-model="searchForm.status" clearable :placeholder="$t('common.placeholder.selectTips')">
            <el-option v-for="item in userStatusList" :key="item.statusId" :label="item.statusName" :value="item.statusId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="btn-form">
          <el-button type="primary" @click="searchEvent" v-hasPerm="['sys:account:search']">{{ $t('common.search') }}</el-button>
          <el-button @click="resetEvent" v-hasPerm="['sys:account:reset']">{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="table-info">
      <div class="mb10">
        <el-button @click="openAdd" type="primary" v-hasPerm="['sys:account:add']">{{ $t('accountManagement.button.addAccunt') }}</el-button>
      </div>
      <el-table v-loading="tableLoading" stripe :data="tableData" style="width: 100%">
        <el-table-column prop="userName" :label="$t('accountManagement.label.account')" min-width="70"></el-table-column>
        <el-table-column prop="nickName" :label="$t('accountManagement.label.name')" min-width="70"/>
        <el-table-column prop="mobile" :label="$t('accountManagement.label.phone')" min-width="70">
          <template #default="scope">
              <span class="encryptBox">
                    {{scope.row.countryAreaCode}}
                    <span v-if="scope.row.mobile && scope.row.mobile.length <= 4">{{scope.row.mobile}}</span>
                    <span v-else>
                      {{scope.row.mobile}}
                      <el-icon
                              v-hasPermiEye="['sys:account:eye']"
                              v-if="scope.row.mobile"
                              @click="scope.row.mobilePhoneShow ? getRealPhone(scope.row.userId,scope.$index) :''"
                              class="encryptBox-icon"
                              color="#762ADB"
                              size="16"
                      >
                        <component :is="scope.row.mobilePhoneShow ? 'View' : ''" />
                      </el-icon>
                    </span>
                </span>
          </template>
        </el-table-column>
        <el-table-column prop="employeeNo" :label="$t('accountManagement.label.employeeNo')" min-width="50"></el-table-column>
        <el-table-column prop="roles" :label="$t('accountManagement.label.roles')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deptName" :label="$t('accountManagement.label.department')"></el-table-column>
        <el-table-column :label="$t('accountManagement.label.status')">
          <template #default="scope">
            <el-switch :active-text="$t('common.activeBtn')"
                       :inactive-text="$t('common.inactiveBtn')"
                       inline-prompt
                       style="--el-switch-on-color: #762ADB; --el-switch-off-color: #CCCFD5"
                       v-model="scope.row.status"
                       :active-value="1"
                       :inactive-value="0"
                       :disabled="scope.row.userName=='admin'"
                        v-hasPerm="['sys:account:updateStatus']"
                       @change="changeUserStatus(scope.row)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" :label="$t('accountManagement.label.createTime')" min-width="90">
          <template #default="scope">
            <span>{{ parseDateTime(scope.row.createTime, 'dateTime') }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" :label="$t('common.handle')" fixed="right" min-width="100">
          <template #default="scope" fixed="right">
            <el-button type="primary" @click="handleEdit(scope.row)" link :disabled="scope.row.userName=='admin'" v-hasPerm="['sys:account:edit']">
              {{ $t('common.edit') }}
            </el-button>
            <el-button type="primary" @click="handleResetPassword(scope.row)" link :disabled="scope.row.userName=='admin'" v-hasPerm="['sys:account:resetPassword']">
                {{ $t('accountManagement.button.resetPassword') }}
            </el-button>
            <el-button type="text" @click="handleDelete(scope.row)" :disabled="scope.row.userName=='admin'" v-hasPerm="['sys:account:delete']">
              {{ $t('common.delete') }}
            </el-button>
          </template>
        </el-table-column>
        <template #empty>
        <Empty/>
      </template>
      </el-table>
      <pagination
        v-show="tableTotal > 0"
        :total="tableTotal"
        v-model:page="searchForm.page"
        v-model:limit="searchForm.limit"
        @pagination="getTableList"
      />
    </div>
    <Edit class="userEditDialog"
          v-model="userDialog.open"
          ref="cmtUser"
          :title="userDialog.title"
          @submitted="submitUser"
    />
  </div>
</template>

<script setup name="accountManagement">
import Edit from './edit.vue'
import {queryUserListPage, updateUserStatus, resetPassword, queryRealPhone} from '@/api/organization.js'
import {ElMessage} from "element-plus";
import {encryptPhone} from '@/utils/index.js'
import { baseConfig } from '@/utils/config';

const {proxy} = getCurrentInstance()

let data = reactive({
  searchForm: initForm(),
  tableLoading: false,
  tableTotal: 0,
  tableData: [],
})

let userDialog = ref({
  open: false,
  title: ''
})

let {searchForm, tableData, tableTotal, tableLoading, queryParams} = toRefs(data);

const userStatusList = ref([
  {
    statusId: ' ',
    statusName: proxy.$t('common.statusEmun.all')
  },
  {
    statusId: 0,
    statusName: proxy.$t('common.statusEmun.disable')
  },
  {
    statusId: 1,
    statusName: proxy.$t('common.statusEmun.enable')
  }
])

function initForm() {
  return {
    nickName: '',
    mobile: '',
    status: ' ',
    page: 1,
    limit: 20,
  }
}

function getTableList() {
  tableLoading.value = true
  queryUserListPage(searchForm.value)
    .then(res => {
      if (res.code !== 0) {
      } else {
        tableData.value = res.data.records.map((item, index) => {
          // item.mobilePhoneShow = false
          item.mobilePhoneShow = true;
          item.roles = item.baseRoleVOList?item.baseRoleVOList.map((it) => it.roleName).join(','):[];
          return {...item};
        })
        tableTotal.value = parseInt(res.data.total)
      }
    }).finally(() => tableLoading.value = false)
}

searchEvent()

function searchEvent() {
  searchForm.value.page = 1
  getTableList()
}

function resetEvent() {
  searchForm.value = initForm()
  getTableList()
}

let cmtUser = ref(null)

// 重置密码
function handleResetPassword(row) {
    ElMessageBox.confirm(proxy.$t('accountManagement.message.resetPasswordTips'), proxy.$t('common.tipTitle'), {
        confirmButtonText: proxy.$t('common.confirm'),
        cancelButtonText: proxy.$t('common.cancel'),
        type: "warning",
    }).then(() => {
        let params = {
            userId : row.userId
        }
        resetPassword(params).then(res => {
            if (res.code === 0) {
                ElMessage.success(proxy.$t('accountManagement.message.resertPasswordSucess'))
                getTableList()
            } else {
                ElMessage.success(proxy.$t('accountManagement.message.resertPasswordFail'))
            }
        })
    })
}

// 新增角色
 function openAdd() {
  cmtUser.value.getAreaList()
  cmtUser.value.setEditType("add")
  userDialog.value.open = true
  userDialog.value.title = proxy.$t('accountManagement.title.addAccountTitle')
  cmtUser.value.getDeptList()
  cmtUser.value.queryAllRoleList()
}

// 编辑角色
function handleEdit(row) {
    cmtUser.value.getAreaList()
    cmtUser.value.setEditType("edit")
    row.account=row.userName
    row.password='********'
    cmtUser.value.setFormData(row)
    userDialog.value.open = true
    userDialog.value.title = proxy.$t('accountManagement.title.editAccountTitle')
    cmtUser.value.getDeptList()
    cmtUser.value.queryAllRoleList()
}

// 删除
function handleDelete(row) {
  if(row.status==1){
      return  ElMessage.error(proxy.$t('accountManagement.message.deleteNotTips'))
  }
  ElMessageBox.confirm(proxy.$t('accountManagement.message.deleteTips'), proxy.$t('common.tipTitle'), {
    confirmButtonText: proxy.$t('common.confirm'),
    cancelButtonText: proxy.$t('common.cancel'),
    type: "warning",
  })
    .then(() => {
      let params = {
        appId: baseConfig.appId,
        userId: row.userId,
        status: 2
      }
      updateUserStatus(params).then(res => {
        if (res.code === 0) {
          ElMessage.success(proxy.$t('accountManagement.message.deleteSucess'))
          searchEvent()
        } else {
            ElMessage.success(proxy.$t('accountManagement.message.deleteFail'))
        }
      })
    })
}

// 修改角色状态
function changeUserStatus(row) {
  let flag = row.status
  row.status = row.status === 0 ? 1 : 0//保持switch点击前的状态
    let params = {
        appId: baseConfig.appId,
        userId: row.userId,
        status: flag
    }
    if(flag===0){
        ElMessageBox.confirm(proxy.$t('accountManagement.message.disableTips'), proxy.$t('common.tipTitle'), {
            confirmButtonText: proxy.$t('common.confirm'),
            cancelButtonText: proxy.$t('common.cancel'),
            type: "warning",
        }).then(() => {
            updateUserStatus(params).then(res => {
                if (res.code === 0) {
                    ElMessage.success(proxy.$t('accountManagement.message.disableSucess'))
                    getTableList()
                } else {
                    ElMessage.success(proxy.$t('accountManagement.message.disableFail'))
                }
            })
        })
    }else{
        updateUserStatus(params).then(res => {
            if (res.code === 0) {
                ElMessage.success(proxy.$t('accountManagement.message.enableSucess'))
                getTableList()
            } else {
                ElMessage.success(proxy.$t('accountManagement.message.enableFail'))
            }
        })
    }

}

function submitUser() {
  searchEvent()
}

function getRealPhone(id, index) {
   queryRealPhone({ userId: id })
        .then((data) => {
            tableData.value[index].mobile = data.data.mobile;
            tableData.value[index].mobilePhoneShow = false;
        })
        .finally(() => {});
}

</script>

<style scoped lang="scss">
.el-select {
  width: 220px;
}

.accountManagement {
    :deep(.el-dialog) {
        padding: 0 !important;
    }

    :deep(.el-dialog__header) {
        width: 100%;
        padding: 16px 32px;
        box-sizing: border-box;
        border-bottom: 1px solid #ECEEF1;
    }

    :deep(.el-dialog__body) {
        padding: 32px;
    }

    :deep(.el-dialog__footer) {
        padding-bottom: 12px;
        padding-right: 32px;
        text-align: right;
    }

    .userEditDialog {
        :deep(.el-select) {
            width: 380px;
        }

        :deep(.el-input) {
            width: 380px;
        }
    }

    .encryptBox {
        // display: inline-flex;
        // justify-content: space-between;
        // align-items: center;
        word-wrap: break-word;
        word-break: break-all;
    }

    .encryptBox-icon {
        margin-left: 4px;
        cursor: pointer;
        // align-self: flex-start;
        vertical-align: text-top;
    }
}

.preview-cell {
  display: flex;
  justify-content: space-between;
  padding-right: 4px;
}

.preview-handle {
  text-align: center;
  align-items: center;
  cursor: pointer;
}

</style>
