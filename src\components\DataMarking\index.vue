<!-- DataMasking.vue -->
<template>
  <span class="data-masking" @click="handleToggle">
    <span v-if="!showDecrypted">{{prefix}}{{ maskedValue }}</span>
    <span v-else>{{prefix}}{{ decryptedValue }}</span>
    <el-icon
      v-if="canToggle"
      :class="{ 'icon-eye-open': showDecrypted }"
      class="encryptBox-icon"
      color="#762ADB "
      size="16"
    >
      <component :is="!showDecrypted ? 'View' : ''" />
    </el-icon>
  </span>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useDataMasking } from './useDataMasking'

const props = defineProps({
  value: {
    type: String,
    required: true
  },
  type: {
    type: String,
    required: true,
    validator: (value) => ['name', 'phone', 'idCard', 'email'].includes(value)
  },
  // 是否允许点击查看明文
  toggleable: {
    type: Boolean,
    default: true
  },
  // 解密接口
  decryptApi: {
    type: Function,
    default: null
  },
  prefix: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['decrypt', 'error'])

const { maskData } = useDataMasking()
const showDecrypted = ref(false)
const decryptedValue = ref('')
const loading = ref(false)

// 判断是否已经是密文（包含*）
const isAlreadyMasked = computed(() => {
  return props.value && props.value.includes('*')
})

// 计算脱敏后的值
const maskedValue = computed(() => {
  if (isAlreadyMasked.value) {
    return props.value
  }
  return maskData(props.value, props.type)
})

// 是否可以切换显示
const canToggle = computed(() => {
  return props.toggleable && props.value
})

// 处理切换显示
const handleToggle = async () => {
  if (!canToggle.value) return

  showDecrypted.value = !showDecrypted.value

  if (showDecrypted.value && !decryptedValue.value) {
    // 如果原始数据已经是密文，需要调用解密接口
    if (isAlreadyMasked.value && props.decryptApi) {
      loading.value = true
      try {
        const result = await props.decryptApi(props.value)
        decryptedValue.value = result
        emit('decrypt', result)
      } catch (error) {
        console.error('解密失败:', error)
        emit('error', error)
        showDecrypted.value = false
      } finally {
        loading.value = false
      }
    } else {
      // 如果原始数据是明文，直接显示
      decryptedValue.value = props.value
    }
  }
}

// 监听value变化，重置状态
watch(() => props.value, () => {
  showDecrypted.value = false
  decryptedValue.value = ''
})
</script>

<style scoped>
.data-masking {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.data-masking:hover {
  color: #1890ff;
}

.icon-eye {
  font-size: 14px;
  cursor: pointer;
  user-select: none;
}

.icon-eye-open {
  opacity: 0.8;
}
</style>